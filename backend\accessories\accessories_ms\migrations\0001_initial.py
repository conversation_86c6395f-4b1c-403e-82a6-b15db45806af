# Generated by Django 5.1.7 on 2025-05-21 14:59

import datetime
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AccessoryCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('type', models.CharField(default='Accessory', max_length=9)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='accessory_category_logos/')),
                ('is_deleted', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='Accessory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON><PERSON>(max_length=100)),
                ('manufacturer_id', models.IntegerField()),
                ('supplier_id', models.PositiveIntegerField(blank=True, null=True)),
                ('location', models.CharField(max_length=50)),
                ('model_number', models.CharField(blank=True, max_length=50, null=True)),
                ('order_number', models.CharField(blank=True, max_length=30, null=True)),
                ('purchase_date', models.DateField(verbose_name=datetime.datetime(2025, 5, 21, 14, 59, 23, 72207, tzinfo=datetime.timezone.utc))),
                ('purchase_cost', models.DecimalField(decimal_places=2, max_digits=10)),
                ('quantity', models.PositiveIntegerField(default=1)),
                ('minimum_quantity', models.PositiveIntegerField(default=0)),
                ('notes', models.TextField(blank=True, null=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='accessory_images/')),
                ('is_deleted', models.BooleanField(default=False)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accessories_ms.accessorycategory')),
            ],
        ),
    ]
