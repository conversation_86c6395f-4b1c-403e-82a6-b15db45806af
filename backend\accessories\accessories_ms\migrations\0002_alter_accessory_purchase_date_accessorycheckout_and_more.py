# Generated by Django 5.1.7 on 2025-05-29 05:13

import datetime
import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accessories_ms', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='accessory',
            name='purchase_date',
            field=models.DateField(verbose_name=datetime.datetime(2025, 5, 29, 5, 13, 0, 334072, tzinfo=datetime.timezone.utc)),
        ),
        migrations.CreateModel(
            name='AccessoryCheckout',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('to_user_id', models.PositiveIntegerField(blank=True, null=True)),
                ('to_location', models.CharField(blank=True, max_length=50, null=True)),
                ('checkout_date', models.DateField(default=datetime.date(2025, 5, 29))),
                ('return_date', models.DateField(blank=True, null=True)),
                ('condition', models.PositiveSmallIntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(10)])),
                ('notes', models.TextField(blank=True, null=True)),
                ('confirmation_notes', models.TextField(blank=True, null=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='accessory_checkout_images/')),
                ('accessory', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='accessory_checkouts', to='accessories_ms.accessory')),
            ],
        ),
        migrations.CreateModel(
            name='AccessoryCheckin',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('checkin_date', models.DateField(default=datetime.date(2025, 5, 29))),
                ('status', models.CharField(blank=True, choices=[('deployed', 'Deployed'), ('deployable', 'Deployable'), ('ready to deploy', 'Ready to Deploy')], max_length=15, null=True)),
                ('condition', models.PositiveSmallIntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(10)])),
                ('notes', models.TextField(blank=True, null=True)),
                ('location', models.CharField(max_length=50)),
                ('image', models.ImageField(blank=True, null=True, upload_to='accessory_checkin_images/')),
                ('accessory_checkout', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='accessory_checkins', to='accessories_ms.accessorycheckout')),
            ],
        ),
    ]
