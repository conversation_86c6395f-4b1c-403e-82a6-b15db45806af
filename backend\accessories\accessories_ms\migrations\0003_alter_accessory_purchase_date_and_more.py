# Generated by Django 5.2.1 on 2025-09-17 14:21

import datetime
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accessories_ms', '0002_alter_accessory_purchase_date_accessorycheckout_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='accessory',
            name='purchase_date',
            field=models.DateField(verbose_name=datetime.datetime(2025, 9, 17, 14, 21, 27, 565007, tzinfo=datetime.timezone.utc)),
        ),
        migrations.AlterField(
            model_name='accessorycheckin',
            name='checkin_date',
            field=models.DateField(default=datetime.date(2025, 9, 17)),
        ),
        migrations.AlterField(
            model_name='accessorycheckout',
            name='checkout_date',
            field=models.DateField(default=datetime.date(2025, 9, 17)),
        ),
    ]
