# Django
ASSETS_DEBUG=False
ASSETS_SECRET_KEY=&4j%qr=e8td!(t__7ic0*8@yr^&h8*+h$r9x5b3mr%$23*_p1a
ASSETS_ALLOWED_HOSTS=localhost, 127.0.0.1, assets-service-production.up.railway.app

# Database
ASSETSS_DATABASE_URL=postgresql://postgres:<EMAIL>:34622/railway
ASSETS_DB_NAME=railway
ASSETS_DB_USER=postgres
ASSETS_DB_PASSWORD=qIPYuoTrNQNVRmZduXdbYhNLqVlRHmPk
ASSETS_DB_HOST=yamabiko.proxy.rlwy.net
ASSETS_DB_PORT=34622

# CORS / CSRF
ASSETS_CORS_ALLOWED_ORIGINS=http://localhost:5173,http://localhost:3000,https://capstone1-production-1c05.up.railway.app
ASSETS_CSRF_TRUSTED_ORIGINS=https://assets-service-production.up.railway.app,https://assets.service.production.up.railway.app,https://assets-service-production-up.railway.app,https://assets.service.production-up.railway.app,https://assets-service.production.up.railway.app
