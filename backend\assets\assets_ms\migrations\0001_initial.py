# Generated by Django 5.1.7 on 2025-09-26 09:45

import datetime
import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Asset',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('displayed_id', models.CharField(max_length=20, unique=True)),
                ('supplier_id', models.PositiveIntegerField()),
                ('location', models.CharField(max_length=50)),
                ('name', models.CharField(max_length=100)),
                ('serial_number', models.CharField(blank=True, max_length=50, null=True)),
                ('warranty_expiration', models.DateField(blank=True, null=True)),
                ('order_number', models.CharField(blank=True, max_length=50, null=True)),
                ('purchase_date', models.DateField(blank=True, null=True)),
                ('purchase_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='asset_images/')),
                ('is_deleted', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='AssetCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('type', models.CharField(default='Asset', max_length=5)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='asset_category_logos/')),
                ('is_deleted', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='Audit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('location', models.CharField(max_length=50)),
                ('user_id', models.IntegerField()),
                ('audit_date', models.DateField(default=datetime.date(2025, 9, 26))),
                ('next_audit_date', models.DateField(default=datetime.date(2025, 9, 26))),
                ('notes', models.TextField(blank=True, null=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(default=datetime.datetime(2025, 9, 26, 9, 45, 13, 505539, tzinfo=datetime.timezone.utc), editable=False)),
            ],
        ),
        migrations.CreateModel(
            name='ComponentCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('type', models.CharField(default='Component', max_length=9)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='component_category_logos/')),
                ('is_deleted', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='Depreciation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=500)),
                ('duration', models.PositiveIntegerField(help_text='Duration in months')),
                ('minimum_value', models.DecimalField(decimal_places=2, max_digits=8)),
                ('is_deleted', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='Manufacturer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('manu_url', models.URLField(blank=True, null=True)),
                ('support_url', models.URLField(blank=True, null=True)),
                ('support_phone', models.CharField(blank=True, max_length=13, null=True)),
                ('support_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='manufacturer_logos/')),
                ('is_deleted', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='Status',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('type', models.CharField(choices=[('deployable', 'Deployable'), ('deployed', 'Deployed'), ('undeployable', 'Undeployable'), ('pending', 'Pending'), ('archived', 'Archived')], max_length=12)),
                ('notes', models.TextField(blank=True, null=True)),
                ('is_deleted', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('address', models.CharField(blank=True, max_length=100, null=True)),
                ('city', models.CharField(blank=True, max_length=50, null=True)),
                ('zip', models.CharField(blank=True, max_length=4, null=True)),
                ('contact_name', models.CharField(blank=True, max_length=100, null=True)),
                ('phone_number', models.CharField(blank=True, max_length=13, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('URL', models.URLField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='supplier_logos/')),
                ('is_deleted', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='AssetCheckout',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('to_user_id', models.PositiveIntegerField()),
                ('to_location', models.CharField()),
                ('checkout_date', models.DateTimeField(auto_now_add=True)),
                ('return_date', models.DateTimeField()),
                ('condition', models.PositiveSmallIntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(10)])),
                ('notes', models.TextField(blank=True, null=True)),
                ('confirmation_notes', models.TextField(blank=True, null=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='asset_checkout_images/')),
                ('asset', models.ForeignKey(limit_choices_to={'is_deleted': False}, on_delete=django.db.models.deletion.CASCADE, related_name='asset_checkouts', to='assets_ms.asset')),
            ],
        ),
        migrations.CreateModel(
            name='AssetCheckin',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('checkin_date', models.DateTimeField()),
                ('condition', models.PositiveSmallIntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(10)])),
                ('notes', models.TextField(blank=True, null=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='asset_checkin_images/')),
                ('asset_checkout', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='asset_checkins', to='assets_ms.assetcheckout')),
            ],
        ),
        migrations.CreateModel(
            name='AuditFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='audit_files/')),
                ('is_deleted', models.BooleanField(default=False)),
                ('audit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='files', to='assets_ms.audit')),
            ],
        ),
        migrations.CreateModel(
            name='AuditSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('notes', models.TextField(blank=True, null=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(default=datetime.datetime(2025, 9, 26, 9, 45, 13, 505230, tzinfo=datetime.timezone.utc), editable=False)),
                ('asset', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='audits', to='assets_ms.asset')),
            ],
        ),
        migrations.AddField(
            model_name='audit',
            name='audit_schedule',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='asset_audits', to='assets_ms.auditschedule'),
        ),
        migrations.CreateModel(
            name='Component',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('manufacturer', models.IntegerField()),
                ('location', models.CharField(max_length=50)),
                ('model_number', models.CharField(blank=True, max_length=50, null=True)),
                ('order_number', models.CharField(blank=True, max_length=30, null=True)),
                ('purchase_date', models.DateField(auto_now_add=True)),
                ('purchase_cost', models.DecimalField(decimal_places=2, max_digits=10)),
                ('quantity', models.PositiveIntegerField(default=1)),
                ('minimum_quantity', models.PositiveIntegerField(default=0)),
                ('notes', models.TextField(blank=True, null=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='component_images/')),
                ('is_deleted', models.BooleanField(default=False)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='assets_ms.componentcategory')),
            ],
        ),
        migrations.CreateModel(
            name='ComponentCheckout',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(default=1)),
                ('checkout_date', models.DateTimeField(auto_now_add=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('component', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='components_checkouts', to='assets_ms.component')),
                ('to_asset', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='checkout_to', to='assets_ms.asset')),
            ],
        ),
        migrations.CreateModel(
            name='ComponentCheckin',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('checkin_date', models.DateTimeField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('component_checkout', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='component_checkins', to='assets_ms.componentcheckout')),
            ],
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('manufacturer_id', models.PositiveIntegerField()),
                ('model_number', models.CharField(blank=True, max_length=50, null=True)),
                ('end_of_life', models.DateField(blank=True, null=True)),
                ('default_purchase_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('default_supplier_id', models.PositiveIntegerField(blank=True, null=True)),
                ('minimum_quantity', models.PositiveIntegerField(default=1)),
                ('operating_system', models.CharField(blank=True, choices=[('linux', 'Linux'), ('windows', 'Windows'), ('macos', 'macOS'), ('ubuntu', 'Ubuntu'), ('centos', 'CentOS'), ('debian', 'Debian'), ('fedora', 'Fedora'), ('other', 'Other')], max_length=7, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='product_images/')),
                ('is_deleted', models.BooleanField(default=False)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='assets_ms.assetcategory')),
                ('depreciation', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='depreciations', to='assets_ms.depreciation')),
            ],
        ),
        migrations.AddField(
            model_name='asset',
            name='product',
            field=models.ForeignKey(limit_choices_to={'is_deleted': False}, on_delete=django.db.models.deletion.CASCADE, related_name='product_assets', to='assets_ms.product'),
        ),
        migrations.CreateModel(
            name='Repair',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('maintenance', 'Maintenance'), ('repair', 'Repair'), ('upgrade', 'Upgrade'), ('test', 'Test'), ('hardware', 'Hardware'), ('software', 'Software')], max_length=20)),
                ('name', models.CharField(max_length=100)),
                ('start_date', models.DateField(auto_now_add=True)),
                ('end_date', models.DateField(blank=True, null=True)),
                ('cost', models.DecimalField(decimal_places=2, max_digits=10)),
                ('notes', models.TextField(blank=True, null=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('asset', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='repair_assets', to='assets_ms.asset')),
            ],
        ),
        migrations.CreateModel(
            name='RepairFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='repair_files/')),
                ('is_deleted', models.BooleanField(default=False)),
                ('repair', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='files', to='assets_ms.repair')),
            ],
        ),
        migrations.AddField(
            model_name='asset',
            name='status',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='status_assets', to='assets_ms.status'),
        ),
    ]
