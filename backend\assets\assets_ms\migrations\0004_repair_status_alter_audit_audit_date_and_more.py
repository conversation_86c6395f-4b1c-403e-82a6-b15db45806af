# Generated by Django 5.1.7 on 2025-10-01 11:25

import datetime
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('assets_ms', '0003_alter_audit_created_at_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='repair',
            name='status',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='repair_status', to='assets_ms.status'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='audit',
            name='audit_date',
            field=models.DateField(default=datetime.date(2025, 10, 1)),
        ),
        migrations.AlterField(
            model_name='audit',
            name='created_at',
            field=models.DateTimeField(default=datetime.datetime(2025, 10, 1, 11, 19, 23, 265842, tzinfo=datetime.timezone.utc), editable=False),
        ),
        migrations.Alter<PERSON>ield(
            model_name='audit',
            name='next_audit_date',
            field=models.DateField(default=datetime.date(2025, 10, 1)),
        ),
        migrations.AlterField(
            model_name='auditschedule',
            name='created_at',
            field=models.DateTimeField(default=datetime.datetime(2025, 10, 1, 11, 19, 23, 265412, tzinfo=datetime.timezone.utc), editable=False),
        ),
        migrations.AlterField(
            model_name='status',
            name='type',
            field=models.CharField(choices=[('deployable', 'Deployable'), ('deployed', 'Deployed'), ('undeployable', 'Undeployable'), ('archived', 'Archived')], max_length=12),
        ),
    ]
