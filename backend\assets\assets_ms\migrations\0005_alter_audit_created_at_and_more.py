# Generated by Django 5.1.7 on 2025-10-01 11:26

import datetime
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('assets_ms', '0004_repair_status_alter_audit_audit_date_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='audit',
            name='created_at',
            field=models.DateTimeField(default=datetime.datetime(2025, 10, 1, 11, 26, 33, 275550, tzinfo=datetime.timezone.utc), editable=False),
        ),
        migrations.AlterField(
            model_name='auditschedule',
            name='created_at',
            field=models.DateTimeField(default=datetime.datetime(2025, 10, 1, 11, 26, 33, 274993, tzinfo=datetime.timezone.utc), editable=False),
        ),
        migrations.AlterField(
            model_name='status',
            name='type',
            field=models.CharField(choices=[('deployable', 'Deployable'), ('deployed', 'Deployed'), ('undeployable', 'Undeployable'), ('pending', 'Pending'), ('archived', 'Archived')], max_length=12),
        ),
    ]
