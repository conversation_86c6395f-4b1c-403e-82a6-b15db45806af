# Generated by Django 5.1.7 on 2025-10-16 12:32

import datetime
import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('assets_ms', '0005_alter_audit_created_at_and_more'),
    ]

    operations = [
        migrations.DeleteModel(
            name='Manufacturer',
        ),
        migrations.DeleteModel(
            name='Supplier',
        ),
        migrations.AddField(
            model_name='component',
            name='supplier',
            field=models.PositiveIntegerField(default=1),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='componentcheckin',
            name='quantity',
            field=models.PositiveIntegerField(default=1),
        ),
        migrations.AlterField(
            model_name='asset',
            name='location',
            field=models.PositiveIntegerField(),
        ),
        migrations.AlterField(
            model_name='assetcheckin',
            name='asset_checkout',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='asset_checkin', to='assets_ms.assetcheckout'),
        ),
        migrations.AlterField(
            model_name='audit',
            name='audit_date',
            field=models.DateField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name='audit',
            name='audit_schedule',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='schedule_audit', to='assets_ms.auditschedule'),
        ),
        migrations.AlterField(
            model_name='audit',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now, editable=False),
        ),
        migrations.AlterField(
            model_name='audit',
            name='next_audit_date',
            field=models.DateField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name='auditschedule',
            name='created_at',
            field=models.DateTimeField(default=datetime.datetime(2025, 10, 16, 12, 31, 37, 112756, tzinfo=datetime.timezone.utc), editable=False),
        ),
        migrations.AlterField(
            model_name='component',
            name='location',
            field=models.PositiveIntegerField(),
        ),
        migrations.AlterField(
            model_name='componentcheckin',
            name='checkin_date',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='repair',
            name='start_date',
            field=models.DateField(default=django.utils.timezone.now),
        ),
    ]
