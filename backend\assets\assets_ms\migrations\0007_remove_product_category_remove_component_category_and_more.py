# Generated by Django 5.1.7 on 2025-10-17 06:15

import datetime
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('assets_ms', '0006_delete_manufacturer_delete_supplier_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='product',
            name='category',
        ),
        migrations.RemoveField(
            model_name='component',
            name='category',
        ),
        migrations.RemoveField(
            model_name='product',
            name='depreciation',
        ),
        migrations.RemoveField(
            model_name='asset',
            name='status',
        ),
        migrations.RemoveField(
            model_name='repair',
            name='status',
        ),
        migrations.AddField(
            model_name='asset',
            name='status_id',
            field=models.PositiveIntegerField(default=1),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='component',
            name='category_id',
            field=models.PositiveIntegerField(default=1),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='product',
            name='category_id',
            field=models.PositiveIntegerField(default=1),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='product',
            name='depreciation_id',
            field=models.PositiveIntegerField(default=1),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='repair',
            name='status_id',
            field=models.PositiveIntegerField(default=1),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='auditschedule',
            name='created_at',
            field=models.DateTimeField(default=datetime.datetime(2025, 10, 17, 6, 15, 4, 735988, tzinfo=datetime.timezone.utc), editable=False),
        ),
        migrations.DeleteModel(
            name='AssetCategory',
        ),
        migrations.DeleteModel(
            name='ComponentCategory',
        ),
        migrations.DeleteModel(
            name='Depreciation',
        ),
        migrations.DeleteModel(
            name='Status',
        ),
    ]
