# Django
AUTH_DEBUG=True
AUTH_SECRET_KEY=bmy*yf(nj2w_io=o+pixv4sdmh^8go$77_f$+o(z!nlx#vq6e6
AUTH_ALLOWED_HOSTS=localhost, 127.0.0.1, authentication-service-production-d804.up.railway.app

# Database
AUTH_DATABASE_URL=postgresql://postgres:<EMAIL>:30598/railway
AUTH_DB_NAME=railway
AUTH_DB_USER=postgres
AUTH_DB_PASSWORD=RiVzhNwutvBgKykkPOSXuqqkHeMEdBbl
AUTH_DB_HOST=turntable.proxy.rlwy.net
AUTH_DB_PORT=30598

# CORS / CSRF
AUTH_CORS_ALLOWED_ORIGINS=http://localhost:5173,http://localhost:3000,https://capstone1-production-1c05.up.railway.app
AUTH_CSRF_TRUSTED_ORIGINS=https://authentication-service-production.up.railway.app,https://authentication.service.production.up.railway.app,https://authentication-service-production-up.railway.app,https://authentication.service.production-up.railway.app,https://authentication-service.production.up.railway.app

# Email configuration
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=jton xyex bxll tluw
DEFAULT_FROM_EMAIL=<EMAIL>