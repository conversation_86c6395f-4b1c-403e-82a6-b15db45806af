# Django
CONTEXTS_DEBUG=True
CONTEXTS_SECRET_KEY=cc#xd9492pf7$zwz)k7qkl)*fn1a%(vw-bbwjhncg3)f+-=9y5
CONTEXTS_ALLOWED_HOSTS=localhost, 127.0.0.1, contexts-service-production.up.railway.app

# Database
CONTEXTS_DATABASE_URL=postgresql://postgres:<EMAIL>:55651/railway
CONTEXTS_DB_NAME=railway
CONTEXTS_DB_USER=postgres
CONTEXTS_DB_PASSWORD=CuioVuejPkcxqFZFpkFLAlfCuytKgsaH
CONTEXTS_DB_HOST=yamabiko.proxy.rlwy.net
CONTEXTS_DB_PORT=55651

# CORS / CSRF
CONTEXTS_CORS_ALLOWED_ORIGINS=http://localhost:5173,http://localhost:3000,https://capstone1-production-1c05.up.railway.app
CONTEXTS_CSRF_TRUSTED_ORIGINS=https://contexts-service-production.up.railway.app,https://contexts.service.production.up.railway.app,https://contexts-service-production-up.railway.app,https://contexts.service.production-up.railway.app,https://contexts-service.production.up.railway.app
