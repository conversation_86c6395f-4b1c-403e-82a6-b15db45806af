# Generated by Django 5.1.7 on 2025-05-21 15:22

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Manufacturer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON><PERSON>(max_length=50)),
                ('manu_url', models.URLField(blank=True, null=True)),
                ('support_url', models.URLField(blank=True, null=True)),
                ('support_phone', models.CharField(blank=True, max_length=13, null=True)),
                ('support_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='manufacturer_logos/')),
                ('is_deleted', models.Boolean<PERSON>ield(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('address', models.CharField(blank=True, max_length=100, null=True)),
                ('city', models.CharField(blank=True, max_length=50, null=True)),
                ('zip', models.PositiveSmallIntegerField(blank=True, max_length=4, null=True)),
                ('contact_name', models.CharField(blank=True, max_length=100, null=True)),
                ('phone_number', models.CharField(blank=True, max_length=13, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('URL', models.URLField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='supplier_logos/')),
                ('is_deleted', models.BooleanField(default=False)),
            ],
        ),
    ]
