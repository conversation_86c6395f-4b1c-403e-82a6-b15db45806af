# Generated by Django 5.1.7 on 2025-10-16 12:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contexts_ms', '0004_checkout'),
    ]

    operations = [
        migrations.CreateModel(
            name='Ticket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ticket_id', models.CharField(max_length=100, unique=True)),
                ('asset_id', models.IntegerField(blank=True, null=True)),
                ('requestor', models.CharField(max_length=100)),
                ('requestor_location', models.CharField(max_length=255)),
                ('requestor_id', models.IntegerField(blank=True, null=True)),
                ('checkout_date', models.DateField(blank=True, null=True)),
                ('checkin_date', models.DateField(blank=True, null=True)),
                ('return_date', models.DateField(blank=True, null=True)),
                ('is_resolved', models.<PERSON><PERSON>anField(default=False)),
                ('checkout_ref_id', models.CharField(blank=True, default='1', max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('condition', models.IntegerField(blank=True, default=1, null=True)),
            ],
            options={
                'verbose_name': 'Asset Checkout',
                'verbose_name_plural': 'Asset Checkouts',
                'ordering': ['-checkout_date'],
            },
        ),
        migrations.DeleteModel(
            name='Checkout',
        ),
    ]
