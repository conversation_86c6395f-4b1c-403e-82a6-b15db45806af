# Generated by Django 5.1.7 on 2025-10-17 06:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contexts_ms', '0005_ticket_delete_checkout'),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('type', models.CharField(choices=[('asset', 'Asset'), ('component', 'Component')], max_length=9)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='category_logos/')),
                ('is_deleted', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='Depreciation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=500)),
                ('duration', models.PositiveIntegerField(help_text='Duration in months')),
                ('minimum_value', models.DecimalField(decimal_places=2, max_digits=8)),
                ('is_deleted', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='Status',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('type', models.CharField(choices=[('deployable', 'Deployable'), ('deployed', 'Deployed'), ('undeployable', 'Undeployable'), ('pending', 'Pending'), ('archived', 'Archived')], max_length=12)),
                ('notes', models.TextField(blank=True, null=True)),
                ('is_deleted', models.BooleanField(default=False)),
            ],
        ),
    ]
