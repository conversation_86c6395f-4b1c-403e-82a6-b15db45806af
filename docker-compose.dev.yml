services:
  frontend:
    build:
      context: ./frontend
      target: development
    container_name: frontend-dev
    ports:
      - "8000:5173"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
    - CHOKIDAR_USEPOLLING=true
    - WATCHPACK_POLLING=true
    networks:
      - app-network
      
  db:
    image: postgres:15
    container_name: postgres-db
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: capstone
      POSTGRES_DB: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sh:/docker-entrypoint-initdb.d/init-db.sh:ro
    networks:
      - app-network

  authentication:
    build:
      context: ./backend/authentication
      target: development
    container_name: authentication-service
    ports:
      - "8001:8001"
    env_file:
      - ./backend/authentication/.env
    volumes:
      - ./backend/authentication:/app
    networks:
      - app-network
    depends_on:
      - db

  assets:
    build:
      context: ./backend/assets
      target: development
    container_name: assets-service
    ports:
      - "8002:8002"
    env_file:
      - ./backend/assets/.env
    volumes:
      - ./backend/assets:/app
    networks:
      - app-network
    depends_on:
      - db

  contexts:
    build:
      context: ./backend/contexts
      target: development
    container_name: contexts-service
    ports:
      - "8003:8003"
    env_file:
      - ./backend/contexts/.env
    volumes:
      - ./backend/contexts:/app
    networks:
      - app-network
    depends_on:
      - db

networks:
  app-network:
    driver: bridge

volumes:
  postgres_data:
