services:

  frontend:
    build:
      context: ./frontend
      target: production
    container_name: frontend-prod
    ports:
      - "8000:8000"
    environment:
      - PORT=8000
    networks:
      - app-network

  authentication:
    build:
      context: ./backend/authentication
      target: production
    container_name: authentication-service-prod
    ports:
      - "8001:8001"
    env_file:
      - ./backend/authentication/.env.prod
    networks:
      - app-network

  assets:
    build:
      context: ./backend/assets
      target: production
    container_name: assets-service-prod
    ports:
      - "8002:8002"
    env_file:
      - ./backend/assets/.env.prod
    networks:
      - app-network
    
  contexts:
    build:
      context: ./backend/contexts
      target: production
    container_name: contexts-service-prod
    ports:
      - "8003:8003"
    env_file:
      - ./backend/contexts/.env.prod
    networks:
      - app-network

networks:
  app-network:
    driver: bridge



