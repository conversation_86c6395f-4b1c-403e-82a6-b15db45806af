# This file is distributed under the same license as the Django package.
# 
# Translators:
# <PERSON><PERSON><PERSON>` <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON>, 2022
# <PERSON>, 2019-2021,2023-2024
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2011-2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-05-22 11:46-0300\n"
"PO-Revision-Date: 2024-08-07 06:49+0000\n"
"Last-Translator: <PERSON>, 2019-2021,2023-2024\n"
"Language-Team: Serbian (Latin) (http://app.transifex.com/django/django/language/sr@latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#: conf/global_settings.py:52
msgid "Afrikaans"
msgstr "afrikanski"

#: conf/global_settings.py:53
msgid "Arabic"
msgstr "arapski"

#: conf/global_settings.py:54
msgid "Algerian Arabic"
msgstr "Alžirski arapski"

#: conf/global_settings.py:55
msgid "Asturian"
msgstr "asturijski"

#: conf/global_settings.py:56
msgid "Azerbaijani"
msgstr "azerbejdžanski"

#: conf/global_settings.py:57
msgid "Bulgarian"
msgstr "bugarski"

#: conf/global_settings.py:58
msgid "Belarusian"
msgstr "beloruski"

#: conf/global_settings.py:59
msgid "Bengali"
msgstr "bengalski"

#: conf/global_settings.py:60
msgid "Breton"
msgstr "bretonski"

#: conf/global_settings.py:61
msgid "Bosnian"
msgstr "bosanski"

#: conf/global_settings.py:62
msgid "Catalan"
msgstr "katalonski"

#: conf/global_settings.py:63
msgid "Central Kurdish (Sorani)"
msgstr "centralnokurdski (sorani)"

#: conf/global_settings.py:64
msgid "Czech"
msgstr "češki"

#: conf/global_settings.py:65
msgid "Welsh"
msgstr "velški"

#: conf/global_settings.py:66
msgid "Danish"
msgstr "danski"

#: conf/global_settings.py:67
msgid "German"
msgstr "nemački"

#: conf/global_settings.py:68
msgid "Lower Sorbian"
msgstr "donjolužičkosrpski"

#: conf/global_settings.py:69
msgid "Greek"
msgstr "grčki"

#: conf/global_settings.py:70
msgid "English"
msgstr "engleski"

#: conf/global_settings.py:71
msgid "Australian English"
msgstr "australijski engleski"

#: conf/global_settings.py:72
msgid "British English"
msgstr "britanski engleski"

#: conf/global_settings.py:73
msgid "Esperanto"
msgstr "esperanto"

#: conf/global_settings.py:74
msgid "Spanish"
msgstr "španski"

#: conf/global_settings.py:75
msgid "Argentinian Spanish"
msgstr "argentinski španski"

#: conf/global_settings.py:76
msgid "Colombian Spanish"
msgstr "kolumbijski španski"

#: conf/global_settings.py:77
msgid "Mexican Spanish"
msgstr "meksički španski"

#: conf/global_settings.py:78
msgid "Nicaraguan Spanish"
msgstr "nikaragvanski španski"

#: conf/global_settings.py:79
msgid "Venezuelan Spanish"
msgstr "venecuelanski španski"

#: conf/global_settings.py:80
msgid "Estonian"
msgstr "estonski"

#: conf/global_settings.py:81
msgid "Basque"
msgstr "baskijski"

#: conf/global_settings.py:82
msgid "Persian"
msgstr "persijski"

#: conf/global_settings.py:83
msgid "Finnish"
msgstr "finski"

#: conf/global_settings.py:84
msgid "French"
msgstr "francuski"

#: conf/global_settings.py:85
msgid "Frisian"
msgstr "frizijski"

#: conf/global_settings.py:86
msgid "Irish"
msgstr "irski"

#: conf/global_settings.py:87
msgid "Scottish Gaelic"
msgstr "škotski galski"

#: conf/global_settings.py:88
msgid "Galician"
msgstr "galski"

#: conf/global_settings.py:89
msgid "Hebrew"
msgstr "hebrejski"

#: conf/global_settings.py:90
msgid "Hindi"
msgstr "hindu"

#: conf/global_settings.py:91
msgid "Croatian"
msgstr "hrvatski"

#: conf/global_settings.py:92
msgid "Upper Sorbian"
msgstr "gornjolužičkosrpski"

#: conf/global_settings.py:93
msgid "Hungarian"
msgstr "mađarski"

#: conf/global_settings.py:94
msgid "Armenian"
msgstr "jermenski"

#: conf/global_settings.py:95
msgid "Interlingua"
msgstr "interlingva"

#: conf/global_settings.py:96
msgid "Indonesian"
msgstr "indonežanski"

#: conf/global_settings.py:97
msgid "Igbo"
msgstr "Igbo"

#: conf/global_settings.py:98
msgid "Ido"
msgstr "ido"

#: conf/global_settings.py:99
msgid "Icelandic"
msgstr "islandski"

#: conf/global_settings.py:100
msgid "Italian"
msgstr "italijanski"

#: conf/global_settings.py:101
msgid "Japanese"
msgstr "japanski"

#: conf/global_settings.py:102
msgid "Georgian"
msgstr "gruzijski"

#: conf/global_settings.py:103
msgid "Kabyle"
msgstr "kabilski"

#: conf/global_settings.py:104
msgid "Kazakh"
msgstr "kazaški"

#: conf/global_settings.py:105
msgid "Khmer"
msgstr "kambodijski"

#: conf/global_settings.py:106
msgid "Kannada"
msgstr "kanada"

#: conf/global_settings.py:107
msgid "Korean"
msgstr "korejski"

#: conf/global_settings.py:108
msgid "Kyrgyz"
msgstr "Kirgiski"

#: conf/global_settings.py:109
msgid "Luxembourgish"
msgstr "luksemburški"

#: conf/global_settings.py:110
msgid "Lithuanian"
msgstr "litvanski"

#: conf/global_settings.py:111
msgid "Latvian"
msgstr "latvijski"

#: conf/global_settings.py:112
msgid "Macedonian"
msgstr "makedonski"

#: conf/global_settings.py:113
msgid "Malayalam"
msgstr "malajalamski"

#: conf/global_settings.py:114
msgid "Mongolian"
msgstr "mongolski"

#: conf/global_settings.py:115
msgid "Marathi"
msgstr "marathi"

#: conf/global_settings.py:116
msgid "Malay"
msgstr "malajski"

#: conf/global_settings.py:117
msgid "Burmese"
msgstr "burmanski"

#: conf/global_settings.py:118
msgid "Norwegian Bokmål"
msgstr "norveški književni"

#: conf/global_settings.py:119
msgid "Nepali"
msgstr "nepalski"

#: conf/global_settings.py:120
msgid "Dutch"
msgstr "holandski"

#: conf/global_settings.py:121
msgid "Norwegian Nynorsk"
msgstr "norveški novi"

#: conf/global_settings.py:122
msgid "Ossetic"
msgstr "osetinski"

#: conf/global_settings.py:123
msgid "Punjabi"
msgstr "Pandžabi"

#: conf/global_settings.py:124
msgid "Polish"
msgstr "poljski"

#: conf/global_settings.py:125
msgid "Portuguese"
msgstr "portugalski"

#: conf/global_settings.py:126
msgid "Brazilian Portuguese"
msgstr "brazilski portugalski"

#: conf/global_settings.py:127
msgid "Romanian"
msgstr "rumunski"

#: conf/global_settings.py:128
msgid "Russian"
msgstr "ruski"

#: conf/global_settings.py:129
msgid "Slovak"
msgstr "slovački"

#: conf/global_settings.py:130
msgid "Slovenian"
msgstr "slovenački"

#: conf/global_settings.py:131
msgid "Albanian"
msgstr "albanski"

#: conf/global_settings.py:132
msgid "Serbian"
msgstr "srpski"

#: conf/global_settings.py:133
msgid "Serbian Latin"
msgstr "srpski (latinica)"

#: conf/global_settings.py:134
msgid "Swedish"
msgstr "švedski"

#: conf/global_settings.py:135
msgid "Swahili"
msgstr "svahili"

#: conf/global_settings.py:136
msgid "Tamil"
msgstr "tamilski"

#: conf/global_settings.py:137
msgid "Telugu"
msgstr "telugu"

#: conf/global_settings.py:138
msgid "Tajik"
msgstr "Tadžiki"

#: conf/global_settings.py:139
msgid "Thai"
msgstr "tajlandski"

#: conf/global_settings.py:140
msgid "Turkmen"
msgstr "Turkmenski"

#: conf/global_settings.py:141
msgid "Turkish"
msgstr "turski"

#: conf/global_settings.py:142
msgid "Tatar"
msgstr "tatarski"

#: conf/global_settings.py:143
msgid "Udmurt"
msgstr "udmurtski"

#: conf/global_settings.py:144
msgid "Uyghur"
msgstr "Ujgur"

#: conf/global_settings.py:145
msgid "Ukrainian"
msgstr "ukrajinski"

#: conf/global_settings.py:146
msgid "Urdu"
msgstr "Urdu"

#: conf/global_settings.py:147
msgid "Uzbek"
msgstr "Uzbekistanski"

#: conf/global_settings.py:148
msgid "Vietnamese"
msgstr "vijetnamski"

#: conf/global_settings.py:149
msgid "Simplified Chinese"
msgstr "novokineski"

#: conf/global_settings.py:150
msgid "Traditional Chinese"
msgstr "starokineski"

#: contrib/messages/apps.py:15
msgid "Messages"
msgstr "Poruke"

#: contrib/sitemaps/apps.py:8
msgid "Site Maps"
msgstr "Mape sajta"

#: contrib/staticfiles/apps.py:9
msgid "Static Files"
msgstr "Statičke datoteke"

#: contrib/syndication/apps.py:7
msgid "Syndication"
msgstr "Udruživanje sadržaja"

#. Translators: String used to replace omitted page numbers in elided page
#. range generated by paginators, e.g. [1, 2, '…', 5, 6, 7, '…', 9, 10].
#: core/paginator.py:30
msgid "…"
msgstr "…"

#: core/paginator.py:32
msgid "That page number is not an integer"
msgstr "Zadati broj strane nije ceo broj"

#: core/paginator.py:33
msgid "That page number is less than 1"
msgstr "Zadati broj strane je manji od 1"

#: core/paginator.py:34
msgid "That page contains no results"
msgstr "Tražena strana ne sadrži rezultate"

#: core/validators.py:22
msgid "Enter a valid value."
msgstr "Unesite ispravnu vrednost."

#: core/validators.py:70
msgid "Enter a valid domain name."
msgstr "Unesite ispravno ime domena."

#: core/validators.py:104 forms/fields.py:759
msgid "Enter a valid URL."
msgstr "Unesite ispravan URL."

#: core/validators.py:165
msgid "Enter a valid integer."
msgstr "Unesite ispravan ceo broj."

#: core/validators.py:176
msgid "Enter a valid email address."
msgstr "Unesite ispravnu e-mail adresu."

#. Translators: "letters" means latin letters: a-z and A-Z.
#: core/validators.py:259
msgid ""
"Enter a valid “slug” consisting of letters, numbers, underscores or hyphens."
msgstr "Unesite isrpavan „slag“, koji se sastoji od slova, brojki, donjih crta ili cirtica."

#: core/validators.py:267
msgid ""
"Enter a valid “slug” consisting of Unicode letters, numbers, underscores, or"
" hyphens."
msgstr "Unesite ispravan \"slag\", koji se sastoji od Unikod slova, brojki, donjih crta ili crtica."

#: core/validators.py:327 core/validators.py:336 core/validators.py:350
#: db/models/fields/__init__.py:2219
#, python-format
msgid "Enter a valid %(protocol)s address."
msgstr "Unesite ispravnu adresu %(protocol)s."

#: core/validators.py:329
msgid "IPv4"
msgstr "IPv4"

#: core/validators.py:338 utils/ipv6.py:30
msgid "IPv6"
msgstr "IPv6"

#: core/validators.py:352
msgid "IPv4 or IPv6"
msgstr "IPv4 ili IPv6"

#: core/validators.py:341
msgid "Enter only digits separated by commas."
msgstr "Unesite samo brojke razdvojene zapetama."

#: core/validators.py:347
#, python-format
msgid "Ensure this value is %(limit_value)s (it is %(show_value)s)."
msgstr "Ovo polje mora da bude %(limit_value)s (trenutno ima %(show_value)s)."

#: core/validators.py:382
#, python-format
msgid "Ensure this value is less than or equal to %(limit_value)s."
msgstr "Ova vrednost mora da bude manja od %(limit_value)s. ili tačno toliko."

#: core/validators.py:391
#, python-format
msgid "Ensure this value is greater than or equal to %(limit_value)s."
msgstr "Ova vrednost mora biti veća od %(limit_value)s ili tačno toliko."

#: core/validators.py:400
#, python-format
msgid "Ensure this value is a multiple of step size %(limit_value)s."
msgstr "Ova vrednost mora da umnožak veličine koraka %(limit_value)s."

#: core/validators.py:407
#, python-format
msgid ""
"Ensure this value is a multiple of step size %(limit_value)s, starting from "
"%(offset)s, e.g. %(offset)s, %(valid_value1)s, %(valid_value2)s, and so on."
msgstr "Uverite se da je ova vrednost višestruka od veličine koraka %(limit_value)s, počevši od %(offset)s, npr. %(offset)s, %(valid_value1)s, %(valid_value2)s, itd."

#: core/validators.py:439
#, python-format
msgid ""
"Ensure this value has at least %(limit_value)d character (it has "
"%(show_value)d)."
msgid_plural ""
"Ensure this value has at least %(limit_value)d characters (it has "
"%(show_value)d)."
msgstr[0] "Ovo polje mora da ima najmanje %(limit_value)d karakter (trenutno ima %(show_value)d)."
msgstr[1] "Ovo polje mora da ima najmanje %(limit_value)d karaktera (trenutno ima %(show_value)d)."
msgstr[2] "Ovo polje mora da ima %(limit_value)d najmanje karaktera (trenutno ima %(show_value)d )."

#: core/validators.py:457
#, python-format
msgid ""
"Ensure this value has at most %(limit_value)d character (it has "
"%(show_value)d)."
msgid_plural ""
"Ensure this value has at most %(limit_value)d characters (it has "
"%(show_value)d)."
msgstr[0] "Ovo polje ne sme da ima više od %(limit_value)d karaktera (trenutno ima %(show_value)d)."
msgstr[1] "Ovo polje ne sme da ima više od %(limit_value)d karaktera (trenutno ima %(show_value)d)."
msgstr[2] "Ovo polje ne sme da ima više od %(limit_value)d karaktera (trenutno ima %(show_value)d)."

#: core/validators.py:480 forms/fields.py:354 forms/fields.py:393
msgid "Enter a number."
msgstr "Unesite broj."

#: core/validators.py:482
#, python-format
msgid "Ensure that there are no more than %(max)s digit in total."
msgid_plural "Ensure that there are no more than %(max)s digits in total."
msgstr[0] "Ukupno ne može biti više od %(max)s cifre."
msgstr[1] "Ukupno ne može biti više od %(max)s cifre."
msgstr[2] "Ukupno ne može biti više od %(max)s cifara."

#: core/validators.py:487
#, python-format
msgid "Ensure that there are no more than %(max)s decimal place."
msgid_plural "Ensure that there are no more than %(max)s decimal places."
msgstr[0] "Ne može biti više od %(max)s decimale."
msgstr[1] "Ne može biti više od %(max)s decimale."
msgstr[2] "Ne može biti više od %(max)s decimala."

#: core/validators.py:492
#, python-format
msgid ""
"Ensure that there are no more than %(max)s digit before the decimal point."
msgid_plural ""
"Ensure that there are no more than %(max)s digits before the decimal point."
msgstr[0] "Ne može biti više od %(max)s cifre pre decimalnog zapisa."
msgstr[1] "Ne može biti više od %(max)s cifre pre decimalnog zapisa."
msgstr[2] "Ne može biti više od %(max)s cifara pre decimalnog zapisa."

#: core/validators.py:563
#, python-format
msgid ""
"File extension “%(extension)s” is not allowed. Allowed extensions are: "
"%(allowed_extensions)s."
msgstr "Ekstenzija datoteke \"%(extension)s\" nije dozvoljena. Dozvoljene su sledeće ekstenzije: %(allowed_extensions)s."

#: core/validators.py:624
msgid "Null characters are not allowed."
msgstr "'Null' karakteri nisu dozvoljeni."

#: db/models/base.py:1465 forms/models.py:902
msgid "and"
msgstr "i"

#: db/models/base.py:1467
#, python-format
msgid "%(model_name)s with this %(field_labels)s already exists."
msgstr "%(model_name)s sa poljem %(field_labels)s već postoji."

#: db/models/constraints.py:20
#, python-format
msgid "Constraint “%(name)s” is violated."
msgstr "Ograničenje „%(name)s“ je prekršeno."

#: db/models/fields/__init__.py:128
#, python-format
msgid "Value %(value)r is not a valid choice."
msgstr "Vrednost %(value)r nije validna."

#: db/models/fields/__init__.py:129
msgid "This field cannot be null."
msgstr "Ovo polje ne može da ostane prazno."

#: db/models/fields/__init__.py:130
msgid "This field cannot be blank."
msgstr "Ovo polje ne može da ostane prazno."

#: db/models/fields/__init__.py:131
#, python-format
msgid "%(model_name)s with this %(field_label)s already exists."
msgstr "%(model_name)s sa ovom vrednošću %(field_label)s već postoji."

#. Translators: The 'lookup_type' is one of 'date', 'year' or
#. 'month'. Eg: "Title must be unique for pub_date year"
#: db/models/fields/__init__.py:135
#, python-format
msgid ""
"%(field_label)s must be unique for %(date_field_label)s %(lookup_type)s."
msgstr "%(field_label)s mora biti jedinstven(a) za %(date_field_label)s %(lookup_type)s."

#: db/models/fields/__init__.py:174
#, python-format
msgid "Field of type: %(field_type)s"
msgstr "Polje tipa: %(field_type)s"

#: db/models/fields/__init__.py:1157
#, python-format
msgid "“%(value)s” value must be either True or False."
msgstr "Vrednost \"%(value)s\"  mora biti True ili False."

#: db/models/fields/__init__.py:1158
#, python-format
msgid "“%(value)s” value must be either True, False, or None."
msgstr "\"%(value)s\" vrednost mora biti True, False ili None."

#: db/models/fields/__init__.py:1160
msgid "Boolean (Either True or False)"
msgstr "Bulova vrednost (True ili False)"

#: db/models/fields/__init__.py:1210
#, python-format
msgid "String (up to %(max_length)s)"
msgstr "String (najviše %(max_length)s znakova)"

#: db/models/fields/__init__.py:1212
msgid "String (unlimited)"
msgstr "String (neograničeno)"

#: db/models/fields/__init__.py:1316
msgid "Comma-separated integers"
msgstr "Celi brojevi razdvojeni zapetama"

#: db/models/fields/__init__.py:1417
#, python-format
msgid ""
"“%(value)s” value has an invalid date format. It must be in YYYY-MM-DD "
"format."
msgstr "Vrednost \"%(value)s\" nema ispravan format datuma. Mora biti u formatu GGGG-MM-DD."

#: db/models/fields/__init__.py:1421 db/models/fields/__init__.py:1556
#, python-format
msgid ""
"“%(value)s” value has the correct format (YYYY-MM-DD) but it is an invalid "
"date."
msgstr "Vrednost “%(value)s” ima odgovarajući format (GGGG-MM-DD), ali nije validan datum."

#: db/models/fields/__init__.py:1425
msgid "Date (without time)"
msgstr "Datum (bez vremena)"

#: db/models/fields/__init__.py:1552
#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in YYYY-MM-DD "
"HH:MM[:ss[.uuuuuu]][TZ] format."
msgstr "Vrednost “%(value)s” je u nevažećem formatu. Mora se uneti u formatu YYYY-MM-DD HH:MM[:ss[.uuuuuu]][TZ]."

#: db/models/fields/__init__.py:1560
#, python-format
msgid ""
"“%(value)s” value has the correct format (YYYY-MM-DD "
"HH:MM[:ss[.uuuuuu]][TZ]) but it is an invalid date/time."
msgstr "Vrednost “%(value)s” je u odgovarajućem formatu (YYYY-MM-DD HH:MM[:ss[.uuuuuu]][TZ]), ali nije validna vrednost za datum i vreme."

#: db/models/fields/__init__.py:1565
msgid "Date (with time)"
msgstr "Datum (sa vremenom)"

#: db/models/fields/__init__.py:1689
#, python-format
msgid "“%(value)s” value must be a decimal number."
msgstr "Vrednost “%(value)s” mora biti decimalni broj."

#: db/models/fields/__init__.py:1691
msgid "Decimal number"
msgstr "Decimalni broj"

#: db/models/fields/__init__.py:1852
#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in [DD] "
"[[HH:]MM:]ss[.uuuuuu] format."
msgstr "Vrednost “%(value)s” nije u odgovarajućem formatu. Mora biti u formatu [DD] [[HH:]MM:]ss[.uuuuuu]."

#: db/models/fields/__init__.py:1856
msgid "Duration"
msgstr "Vremenski interval"

#: db/models/fields/__init__.py:1908
msgid "Email address"
msgstr "Imejl adresa"

#: db/models/fields/__init__.py:1933
msgid "File path"
msgstr "Putanja fajla"

#: db/models/fields/__init__.py:2011
#, python-format
msgid "“%(value)s” value must be a float."
msgstr "Vrednost “%(value)s” value mora biti tipa float."

#: db/models/fields/__init__.py:2013
msgid "Floating point number"
msgstr "Broj sa pokrenom zapetom"

#: db/models/fields/__init__.py:2053
#, python-format
msgid "“%(value)s” value must be an integer."
msgstr "Vrednost “%(value)s” mora biti ceo broj."

#: db/models/fields/__init__.py:2055
msgid "Integer"
msgstr "Ceo broj"

#: db/models/fields/__init__.py:2151
msgid "Big (8 byte) integer"
msgstr "Veliki ceo broj"

#: db/models/fields/__init__.py:2168
msgid "Small integer"
msgstr "Mali ceo broj"

#: db/models/fields/__init__.py:2176
msgid "IPv4 address"
msgstr "IPv4 adresa"

#: db/models/fields/__init__.py:2207
msgid "IP address"
msgstr "IP adresa"

#: db/models/fields/__init__.py:2300 db/models/fields/__init__.py:2301
#, python-format
msgid "“%(value)s” value must be either None, True or False."
msgstr "Vrednost “%(value)s” mora biti None, True ili False."

#: db/models/fields/__init__.py:2303
msgid "Boolean (Either True, False or None)"
msgstr "Bulova vrednost (True, False ili None)"

#: db/models/fields/__init__.py:2354
msgid "Positive big integer"
msgstr "Velik pozitivan celi broj"

#: db/models/fields/__init__.py:2369
msgid "Positive integer"
msgstr "Pozitivan ceo broj"

#: db/models/fields/__init__.py:2384
msgid "Positive small integer"
msgstr "Pozitivan mali ceo broj"

#: db/models/fields/__init__.py:2400
#, python-format
msgid "Slug (up to %(max_length)s)"
msgstr "Slag (ne duži od %(max_length)s)"

#: db/models/fields/__init__.py:2436
msgid "Text"
msgstr "Tekst"

#: db/models/fields/__init__.py:2511
#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in HH:MM[:ss[.uuuuuu]] "
"format."
msgstr "Vrednost “%(value)s” nije u odgovarajućem formatu. Mora biti u formatu HH:MM[:ss[.uuuuuu]]."

#: db/models/fields/__init__.py:2515
#, python-format
msgid ""
"“%(value)s” value has the correct format (HH:MM[:ss[.uuuuuu]]) but it is an "
"invalid time."
msgstr "Vrednost “%(value)s” je u odgovarajućem formatu (HH:MM[:ss[.uuuuuu]]), ali nije validna vrednost za vreme."

#: db/models/fields/__init__.py:2519
msgid "Time"
msgstr "Vreme"

#: db/models/fields/__init__.py:2627
msgid "URL"
msgstr "URL"

#: db/models/fields/__init__.py:2651
msgid "Raw binary data"
msgstr "Sirovi binarni podaci"

#: db/models/fields/__init__.py:2716
#, python-format
msgid "“%(value)s” is not a valid UUID."
msgstr "Vrednost “%(value)s” nije validan UUID (jedinstveni ID)."

#: db/models/fields/__init__.py:2718
msgid "Universally unique identifier"
msgstr "Univerzalno jedinstveni identifikator"

#: db/models/fields/files.py:232
msgid "File"
msgstr "Fajl"

#: db/models/fields/files.py:393
msgid "Image"
msgstr "Slika"

#: db/models/fields/json.py:26
msgid "A JSON object"
msgstr "JSON objekat"

#: db/models/fields/json.py:28
msgid "Value must be valid JSON."
msgstr "Vrednost mora biti ispravni JSON."

#: db/models/fields/related.py:939
#, python-format
msgid "%(model)s instance with %(field)s %(value)r does not exist."
msgstr "Instanca modela %(model)s sa vrednošću %(field)s %(value)r ne postoji."

#: db/models/fields/related.py:941
msgid "Foreign Key (type determined by related field)"
msgstr "Strani ključ (tip određuje referentno polje)"

#: db/models/fields/related.py:1235
msgid "One-to-one relationship"
msgstr "Relacija jedan na jedan"

#: db/models/fields/related.py:1292
#, python-format
msgid "%(from)s-%(to)s relationship"
msgstr "Relacija %(from)s-%(to)s"

#: db/models/fields/related.py:1294
#, python-format
msgid "%(from)s-%(to)s relationships"
msgstr "Relacije %(from)s-%(to)s"

#: db/models/fields/related.py:1342
msgid "Many-to-many relationship"
msgstr "Relacija više na više"

#. Translators: If found as last label character, these punctuation
#. characters will prevent the default label_suffix to be appended to the
#. label
#: forms/boundfield.py:185
msgid ":?.!"
msgstr ":?.!"

#: forms/fields.py:94
msgid "This field is required."
msgstr "Ovo polje se mora popuniti."

#: forms/fields.py:303
msgid "Enter a whole number."
msgstr "Unesite ceo broj."

#: forms/fields.py:474 forms/fields.py:1246
msgid "Enter a valid date."
msgstr "Unesite ispravan datum."

#: forms/fields.py:497 forms/fields.py:1247
msgid "Enter a valid time."
msgstr "Unesite ispravno vreme"

#: forms/fields.py:524
msgid "Enter a valid date/time."
msgstr "Unesite ispravan datum/vreme."

#: forms/fields.py:558
msgid "Enter a valid duration."
msgstr "Unesite ispravno trajanje."

#: forms/fields.py:559
#, python-brace-format
msgid "The number of days must be between {min_days} and {max_days}."
msgstr "Broj dana mora biti između {min_days} i {max_days}."

#: forms/fields.py:628
msgid "No file was submitted. Check the encoding type on the form."
msgstr "Fajl nije prebačen. Proverite tip enkodiranja formulara."

#: forms/fields.py:629
msgid "No file was submitted."
msgstr "Fajl nije prebačen."

#: forms/fields.py:630
msgid "The submitted file is empty."
msgstr "Prebačen fajl je prazan."

#: forms/fields.py:632
#, python-format
msgid ""
"Ensure this filename has at most %(max)d character (it has %(length)d)."
msgid_plural ""
"Ensure this filename has at most %(max)d characters (it has %(length)d)."
msgstr[0] "Ime fajla ne može imati više od %(max)d karaktera (trenutno ima %(length)d)."
msgstr[1] "Ime fajla ne može imati više od %(max)d karaktera (trenutno ima %(length)d)."
msgstr[2] "Ime fajla ne može imati više od %(max)d karaktera (trenutno ima %(length)d)."

#: forms/fields.py:637
msgid "Please either submit a file or check the clear checkbox, not both."
msgstr "Može se samo poslati fajl ili izbrisati, ne oba."

#: forms/fields.py:701
msgid ""
"Upload a valid image. The file you uploaded was either not an image or a "
"corrupted image."
msgstr "Prebacite ispravan fajl. Fajl koji je prebačen ili nije slika, ili je oštećen."

#: forms/fields.py:868 forms/fields.py:954 forms/models.py:1581
#, python-format
msgid "Select a valid choice. %(value)s is not one of the available choices."
msgstr "%(value)s nije među ponuđenim vrednostima. Odaberite jednu od ponuđenih."

#: forms/fields.py:956 forms/fields.py:1075 forms/models.py:1579
msgid "Enter a list of values."
msgstr "Unesite listu vrednosti."

#: forms/fields.py:1076
msgid "Enter a complete value."
msgstr "Unesite kompletnu vrednost."

#: forms/fields.py:1315
msgid "Enter a valid UUID."
msgstr "Unesite ispravan UUID."

#: forms/fields.py:1345
msgid "Enter a valid JSON."
msgstr "Unesite ispravan JSON."

#. Translators: This is the default suffix added to form field labels
#: forms/forms.py:94
msgid ":"
msgstr ":"

#: forms/forms.py:231
#, python-format
msgid "(Hidden field %(name)s) %(error)s"
msgstr "(Skriveno polje %(name)s) %(error)s"

#: forms/formsets.py:61
#, python-format
msgid ""
"ManagementForm data is missing or has been tampered with. Missing fields: "
"%(field_names)s. You may need to file a bug report if the issue persists."
msgstr "Podaci od ManagementForm nedostaju ili su pokvareni. Polja koja nedostaju: %(field_names)s. Možda će biti potrebno da prijavite grešku ako se problem nastavi."

#: forms/formsets.py:65
#, python-format
msgid "Please submit at most %(num)d form."
msgid_plural "Please submit at most %(num)d forms."
msgstr[0] "Molim prosledite najviše %(num)d formular."
msgstr[1] "Molim prosledite najviše %(num)d formulara."
msgstr[2] "Molim prosledite najviše %(num)d formulara."

#: forms/formsets.py:70
#, python-format
msgid "Please submit at least %(num)d form."
msgid_plural "Please submit at least %(num)d forms."
msgstr[0] " Molim prosledite najmanje %(num)d formular."
msgstr[1] " Molim prosledite najmanje %(num)d formulara."
msgstr[2] " Molim prosledite najmanje %(num)d formulara."

#: forms/formsets.py:484 forms/formsets.py:491
msgid "Order"
msgstr "Redosled"

#: forms/formsets.py:499
msgid "Delete"
msgstr "Obriši"

#: forms/models.py:895
#, python-format
msgid "Please correct the duplicate data for %(field)s."
msgstr "Ispravite dupliran sadržaj za polja: %(field)s."

#: forms/models.py:900
#, python-format
msgid "Please correct the duplicate data for %(field)s, which must be unique."
msgstr "Ispravite dupliran sadržaj za polja: %(field)s, koji mora da bude jedinstven."

#: forms/models.py:907
#, python-format
msgid ""
"Please correct the duplicate data for %(field_name)s which must be unique "
"for the %(lookup)s in %(date_field)s."
msgstr "Ispravite dupliran sadržaj za polja: %(field_name)s, koji mora da bude jedinstven za %(lookup)s u %(date_field)s."

#: forms/models.py:916
msgid "Please correct the duplicate values below."
msgstr "Ispravite duplirane vrednosti dole."

#: forms/models.py:1353
msgid "The inline value did not match the parent instance."
msgstr "Direktno uneta vrednost ne odgovara instanci roditelja."

#: forms/models.py:1444
msgid ""
"Select a valid choice. That choice is not one of the available choices."
msgstr "Odabrana vrednost nije među ponuđenima. Odaberite jednu od ponuđenih."

#: forms/models.py:1583
#, python-format
msgid "“%(pk)s” is not a valid value."
msgstr "\"%(pk)s\" nije ispravna vrednost."

#: forms/utils.py:227
#, python-format
msgid ""
"%(datetime)s couldn’t be interpreted in time zone %(current_timezone)s; it "
"may be ambiguous or it may not exist."
msgstr "Vreme %(datetime)s se ne može protumačiti u vremenskoj zoni %(current_timezone)s; možda je dvosmisleno ili ne postoji."

#: forms/widgets.py:457
msgid "Clear"
msgstr "Očisti"

#: forms/widgets.py:458
msgid "Currently"
msgstr "Trenutno"

#: forms/widgets.py:459
msgid "Change"
msgstr "Izmeni"

#: forms/widgets.py:796
msgid "Unknown"
msgstr "Nepoznato"

#: forms/widgets.py:797
msgid "Yes"
msgstr "Da"

#: forms/widgets.py:798
msgid "No"
msgstr "Ne"

#. Translators: Please do not add spaces around commas.
#: template/defaultfilters.py:875
msgid "yes,no,maybe"
msgstr "da,ne,možda"

#: template/defaultfilters.py:905 template/defaultfilters.py:922
#, python-format
msgid "%(size)d byte"
msgid_plural "%(size)d bytes"
msgstr[0] "%(size)d bajt"
msgstr[1] "%(size)d bajta"
msgstr[2] "%(size)d bajtova"

#: template/defaultfilters.py:924
#, python-format
msgid "%s KB"
msgstr "%s KB"

#: template/defaultfilters.py:926
#, python-format
msgid "%s MB"
msgstr "%s MB"

#: template/defaultfilters.py:928
#, python-format
msgid "%s GB"
msgstr "%s GB"

#: template/defaultfilters.py:930
#, python-format
msgid "%s TB"
msgstr "%s TB"

#: template/defaultfilters.py:932
#, python-format
msgid "%s PB"
msgstr "%s PB"

#: utils/dateformat.py:73
msgid "p.m."
msgstr "po p."

#: utils/dateformat.py:74
msgid "a.m."
msgstr "pre p."

#: utils/dateformat.py:79
msgid "PM"
msgstr "PM"

#: utils/dateformat.py:80
msgid "AM"
msgstr "AM"

#: utils/dateformat.py:152
msgid "midnight"
msgstr "ponoć"

#: utils/dateformat.py:154
msgid "noon"
msgstr "podne"

#: utils/dates.py:7
msgid "Monday"
msgstr "ponedeljak"

#: utils/dates.py:8
msgid "Tuesday"
msgstr "utorak"

#: utils/dates.py:9
msgid "Wednesday"
msgstr "sreda"

#: utils/dates.py:10
msgid "Thursday"
msgstr "četvrtak"

#: utils/dates.py:11
msgid "Friday"
msgstr "petak"

#: utils/dates.py:12
msgid "Saturday"
msgstr "subota"

#: utils/dates.py:13
msgid "Sunday"
msgstr "nedelja"

#: utils/dates.py:16
msgid "Mon"
msgstr "pon."

#: utils/dates.py:17
msgid "Tue"
msgstr "uto."

#: utils/dates.py:18
msgid "Wed"
msgstr "sre."

#: utils/dates.py:19
msgid "Thu"
msgstr "čet."

#: utils/dates.py:20
msgid "Fri"
msgstr "pet."

#: utils/dates.py:21
msgid "Sat"
msgstr "sub."

#: utils/dates.py:22
msgid "Sun"
msgstr "ned."

#: utils/dates.py:25
msgid "January"
msgstr "januar"

#: utils/dates.py:26
msgid "February"
msgstr "februar"

#: utils/dates.py:27
msgid "March"
msgstr "mart"

#: utils/dates.py:28
msgid "April"
msgstr "april"

#: utils/dates.py:29
msgid "May"
msgstr "maj"

#: utils/dates.py:30
msgid "June"
msgstr "jun"

#: utils/dates.py:31
msgid "July"
msgstr "jul"

#: utils/dates.py:32
msgid "August"
msgstr "avgust"

#: utils/dates.py:33
msgid "September"
msgstr "septembar"

#: utils/dates.py:34
msgid "October"
msgstr "oktobar"

#: utils/dates.py:35
msgid "November"
msgstr "novembar"

#: utils/dates.py:36
msgid "December"
msgstr "decembar"

#: utils/dates.py:39
msgid "jan"
msgstr "jan."

#: utils/dates.py:40
msgid "feb"
msgstr "feb."

#: utils/dates.py:41
msgid "mar"
msgstr "mar."

#: utils/dates.py:42
msgid "apr"
msgstr "apr."

#: utils/dates.py:43
msgid "may"
msgstr "maj."

#: utils/dates.py:44
msgid "jun"
msgstr "jun."

#: utils/dates.py:45
msgid "jul"
msgstr "jul."

#: utils/dates.py:46
msgid "aug"
msgstr "aug."

#: utils/dates.py:47
msgid "sep"
msgstr "sep."

#: utils/dates.py:48
msgid "oct"
msgstr "okt."

#: utils/dates.py:49
msgid "nov"
msgstr "nov."

#: utils/dates.py:50
msgid "dec"
msgstr "dec."

#: utils/dates.py:53
msgctxt "abbrev. month"
msgid "Jan."
msgstr "Jan."

#: utils/dates.py:54
msgctxt "abbrev. month"
msgid "Feb."
msgstr "Feb."

#: utils/dates.py:55
msgctxt "abbrev. month"
msgid "March"
msgstr "Mart"

#: utils/dates.py:56
msgctxt "abbrev. month"
msgid "April"
msgstr "April"

#: utils/dates.py:57
msgctxt "abbrev. month"
msgid "May"
msgstr "Maj"

#: utils/dates.py:58
msgctxt "abbrev. month"
msgid "June"
msgstr "Jun"

#: utils/dates.py:59
msgctxt "abbrev. month"
msgid "July"
msgstr "Jul"

#: utils/dates.py:60
msgctxt "abbrev. month"
msgid "Aug."
msgstr "Avg."

#: utils/dates.py:61
msgctxt "abbrev. month"
msgid "Sept."
msgstr "Sept."

#: utils/dates.py:62
msgctxt "abbrev. month"
msgid "Oct."
msgstr "Okt."

#: utils/dates.py:63
msgctxt "abbrev. month"
msgid "Nov."
msgstr "Nov."

#: utils/dates.py:64
msgctxt "abbrev. month"
msgid "Dec."
msgstr "Dec."

#: utils/dates.py:67
msgctxt "alt. month"
msgid "January"
msgstr "Januar"

#: utils/dates.py:68
msgctxt "alt. month"
msgid "February"
msgstr "Februar"

#: utils/dates.py:69
msgctxt "alt. month"
msgid "March"
msgstr "Mart"

#: utils/dates.py:70
msgctxt "alt. month"
msgid "April"
msgstr "April"

#: utils/dates.py:71
msgctxt "alt. month"
msgid "May"
msgstr "Maj"

#: utils/dates.py:72
msgctxt "alt. month"
msgid "June"
msgstr "Jun"

#: utils/dates.py:73
msgctxt "alt. month"
msgid "July"
msgstr "Jul"

#: utils/dates.py:74
msgctxt "alt. month"
msgid "August"
msgstr "Avgust"

#: utils/dates.py:75
msgctxt "alt. month"
msgid "September"
msgstr "Septembar"

#: utils/dates.py:76
msgctxt "alt. month"
msgid "October"
msgstr "Oktobar"

#: utils/dates.py:77
msgctxt "alt. month"
msgid "November"
msgstr "Novembar"

#: utils/dates.py:78
msgctxt "alt. month"
msgid "December"
msgstr "Decembar"

#: utils/ipv6.py:8
msgid "This is not a valid IPv6 address."
msgstr "Ovo nije ispravna IPv6 adresa."

#: utils/text.py:70
#, python-format
msgctxt "String to return when truncating text"
msgid "%(truncated_text)s…"
msgstr "%(truncated_text)s..."

#: utils/text.py:255
msgid "or"
msgstr "ili"

#. Translators: This string is used as a separator between list elements
#: utils/text.py:274 utils/timesince.py:135
msgid ", "
msgstr ","

#: utils/timesince.py:8
#, python-format
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d godina"
msgstr[1] "%(num)d godine"
msgstr[2] "%(num)d godina"

#: utils/timesince.py:9
#, python-format
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d mesec"
msgstr[1] "%(num)d meseca"
msgstr[2] "%(num)d meseci"

#: utils/timesince.py:10
#, python-format
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d nedelja"
msgstr[1] "%(num)d nedelje"
msgstr[2] "%(num)d nedelja"

#: utils/timesince.py:11
#, python-format
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d dan"
msgstr[1] "%(num)d dana"
msgstr[2] "%(num)d dana"

#: utils/timesince.py:12
#, python-format
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d sat"
msgstr[1] "%(num)d sata"
msgstr[2] "%(num)d sati"

#: utils/timesince.py:13
#, python-format
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d minut"
msgstr[1] "%(num)d minuta"
msgstr[2] "%(num)d minuta"

#: views/csrf.py:29
msgid "Forbidden"
msgstr "Zabranjeno"

#: views/csrf.py:30
msgid "CSRF verification failed. Request aborted."
msgstr "CSRF verifikacija nije prošla. Zahtev odbijen."

#: views/csrf.py:34
msgid ""
"You are seeing this message because this HTTPS site requires a “Referer "
"header” to be sent by your web browser, but none was sent. This header is "
"required for security reasons, to ensure that your browser is not being "
"hijacked by third parties."
msgstr "Ova poruka je prikazana jer ovaj HTTPS sajt zahteva da \"Referer header\" bude poslat od strane vašeg internet pregledača, što trenutno nije slučaj. Pomenuto zaglavlje je potrebno iz bezbedonosnih razloga, da bi se osiguralo da vaš pregledač nije pod kontrolom trećih lica."

#: views/csrf.py:40
msgid ""
"If you have configured your browser to disable “Referer” headers, please re-"
"enable them, at least for this site, or for HTTPS connections, or for “same-"
"origin” requests."
msgstr "Ako ste podesili internet pregledač da ne šalje \"Referer\" zaglavlja, ponovo ih uključite, barem za ovaj sajt, ili za HTTPS konekcije, ili za \"same-origin\" zahteve."

#: views/csrf.py:45
msgid ""
"If you are using the <meta name=\"referrer\" content=\"no-referrer\"> tag or"
" including the “Referrer-Policy: no-referrer” header, please remove them. "
"The CSRF protection requires the “Referer” header to do strict referer "
"checking. If you’re concerned about privacy, use alternatives like <a "
"rel=\"noreferrer\" …> for links to third-party sites."
msgstr "Ako koristite <meta name=\"referrer\" content=\"no-referrer\"> tag ili \"Referrer-Policy: no-referrer\" zaglavlje, molimo da ih uklonite. CSRF zaštita zahteva \"Referer\" zaglavlje da bi se obavila striktna \"referrer\" provera. Ukoliko vas brine privatnost, koristite alternative kao <a rel=\"noreferrer\" ...> za linkove ka drugim sajtovima."

#: views/csrf.py:54
msgid ""
"You are seeing this message because this site requires a CSRF cookie when "
"submitting forms. This cookie is required for security reasons, to ensure "
"that your browser is not being hijacked by third parties."
msgstr "Ova poruka je prikazana jer ovaj sajt zahteva CSRF kuki kada se prosleđuju podaci iz formi. Ovaj kuki je potreban iz sigurnosnih razloga, da bi se osiguralo da vaš pretraživač nije pod kontrolom trećih lica."

#: views/csrf.py:60
msgid ""
"If you have configured your browser to disable cookies, please re-enable "
"them, at least for this site, or for “same-origin” requests."
msgstr "Ako je vaš internet pregedač podešen da onemogući kolačiće, molimo da ih uključite, barem za ovaj sajt, ili za \"same-origin\" zahteve."

#: views/csrf.py:66
msgid "More information is available with DEBUG=True."
msgstr "Više informacija je dostupno sa DEBUG=True."

#: views/generic/dates.py:44
msgid "No year specified"
msgstr "Godina nije naznačena"

#: views/generic/dates.py:64 views/generic/dates.py:115
#: views/generic/dates.py:214
msgid "Date out of range"
msgstr "Datum van opsega"

#: views/generic/dates.py:94
msgid "No month specified"
msgstr "Mesec nije naznačen"

#: views/generic/dates.py:147
msgid "No day specified"
msgstr "Dan nije naznačen"

#: views/generic/dates.py:194
msgid "No week specified"
msgstr "Nedelja nije naznačena"

#: views/generic/dates.py:349 views/generic/dates.py:380
#, python-format
msgid "No %(verbose_name_plural)s available"
msgstr "Nedostupni objekti %(verbose_name_plural)s"

#: views/generic/dates.py:652
#, python-format
msgid ""
"Future %(verbose_name_plural)s not available because "
"%(class_name)s.allow_future is False."
msgstr "Opcija „future“ nije dostupna za „%(verbose_name_plural)s“ jer %(class_name)s.allow_future ima vrednost False."

#: views/generic/dates.py:692
#, python-format
msgid "Invalid date string “%(datestr)s” given format “%(format)s”"
msgstr "Neispravan datum \"%(datestr)s\" za format \"%(format)s\""

#: views/generic/detail.py:56
#, python-format
msgid "No %(verbose_name)s found matching the query"
msgstr "Nijedan objekat klase %(verbose_name)s nije nađen datim upitom."

#: views/generic/list.py:70
msgid "Page is not “last”, nor can it be converted to an int."
msgstr "Stranica nije poslednja, niti može biti konvertovana u tip \"int\"."

#: views/generic/list.py:77
#, python-format
msgid "Invalid page (%(page_number)s): %(message)s"
msgstr "Neispravna strana (%(page_number)s): %(message)s"

#: views/generic/list.py:169
#, python-format
msgid "Empty list and “%(class_name)s.allow_empty” is False."
msgstr "Prazna lista i „%(class_name)s.allow_empty“ ima vrednost False."

#: views/static.py:48
msgid "Directory indexes are not allowed here."
msgstr "Indeksi direktorijuma nisu dozvoljeni ovde."

#: views/static.py:50
#, python-format
msgid "“%(path)s” does not exist"
msgstr "„%(path)s“ ne postoji"

#: views/static.py:67 views/templates/directory_index.html:8
#: views/templates/directory_index.html:11
#, python-format
msgid "Index of %(directory)s"
msgstr "Indeks direktorijuma %(directory)s"

#: views/templates/default_urlconf.html:7
#: views/templates/default_urlconf.html:220
msgid "The install worked successfully! Congratulations!"
msgstr "Instalacija je prošla uspešno. Čestitke!"

#: views/templates/default_urlconf.html:206
#, python-format
msgid ""
"View <a href=\"https://docs.djangoproject.com/en/%(version)s/releases/\" "
"target=\"_blank\" rel=\"noopener\">release notes</a> for Django %(version)s"
msgstr "Pogledajte <a href=\"https://docs.djangoproject.com/en/%(version)s/releases/\" target=\"_blank\" rel=\"noopener\">napomene uz izdanje</a> za Đango %(version)s"

#: views/templates/default_urlconf.html:221
#, python-format
msgid ""
"You are seeing this page because <a "
"href=\"https://docs.djangoproject.com/en/%(version)s/ref/settings/#debug\" "
"target=\"_blank\" rel=\"noopener\">DEBUG=True</a> is in your settings file "
"and you have not configured any URLs."
msgstr "Ova strana je prikazana jer je <a href=\"https://docs.djangoproject.com/en/%(version)s/ref/settings/#debug\" target=\"_blank\" rel=\"noopener\">DEBUG=True</a> u vašim podešavanjima i niste konfigurisali nijedan URL."

#: views/templates/default_urlconf.html:229
msgid "Django Documentation"
msgstr "Đango dokumentacija"

#: views/templates/default_urlconf.html:230
msgid "Topics, references, &amp; how-to’s"
msgstr "Teme, reference, &amp; kako-da"

#: views/templates/default_urlconf.html:238
msgid "Tutorial: A Polling App"
msgstr "Uputstvo: aplikacija za glasanje"

#: views/templates/default_urlconf.html:239
msgid "Get started with Django"
msgstr "Počnite sa Đangom"

#: views/templates/default_urlconf.html:247
msgid "Django Community"
msgstr "Đango zajednica"

#: views/templates/default_urlconf.html:248
msgid "Connect, get help, or contribute"
msgstr "Povežite se, potražite pomoć ili dajte doprinos"
