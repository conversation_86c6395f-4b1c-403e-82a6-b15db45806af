[{"id": 1, "component": 1, "to_asset": {"id": 1, "displayed_id": "Laptop A123"}, "quantity": 4, "checkout_date": "2025-10-01T09:30:00", "notes": "Upgraded RAM for laptop A123", "total_checked_in": 2, "remaining_quantity": 2, "is_fully_returned": false}, {"id": 2, "component": 1, "to_asset": {"id": 2, "displayed_id": "Laptop B456"}, "quantity": 2, "checkout_date": "2025-10-05T14:15:00", "notes": "RAM upgrade for laptop B456", "total_checked_in": 0, "remaining_quantity": 2, "is_fully_returned": false}, {"id": 3, "component": 1, "to_asset": {"id": 3, "displayed_id": "Desktop D789"}, "quantity": 2, "checkout_date": "2025-10-07T11:00:00", "notes": "RAM added to desktop D789 for performance testing", "total_checked_in": 0, "remaining_quantity": 2, "is_fully_returned": false}, {"id": 4, "component": 2, "to_asset": {"id": 4, "displayed_id": "Desktop D101"}, "quantity": 5, "checkout_date": "2025-09-15T10:00:00", "notes": "SSD replacement for D101", "total_checked_in": 0, "remaining_quantity": 5, "is_fully_returned": false}, {"id": 5, "component": 2, "to_asset": {"id": 5, "displayed_id": "Desktop D102"}, "quantity": 6, "checkout_date": "2025-09-18T14:30:00", "notes": "SSD upgrade for D102", "total_checked_in": 4, "remaining_quantity": 2, "is_fully_returned": false}, {"id": 6, "component": 2, "to_asset": {"id": 6, "displayed_id": "Desktop D103"}, "quantity": 4, "checkout_date": "2025-09-20T09:00:00", "notes": "SSD upgrade for D103", "total_checked_in": 0, "remaining_quantity": 4, "is_fully_returned": false}, {"id": 7, "component": 4, "to_asset": {"id": 10, "displayed_id": "Server S201"}, "quantity": 4, "checkout_date": "2025-09-12T10:00:00", "notes": "Replacement fans for Server S201", "total_checked_in": 0, "remaining_quantity": 4, "is_fully_returned": false}, {"id": 8, "component": 4, "to_asset": {"id": 11, "displayed_id": "Server S202"}, "quantity": 3, "checkout_date": "2025-09-15T09:30:00", "notes": "Cooling fan for Server S202", "total_checked_in": 0, "remaining_quantity": 3, "is_fully_returned": false}, {"id": 9, "component": 4, "to_asset": {"id": 12, "displayed_id": "Server S203"}, "quantity": 3, "checkout_date": "2025-09-18T14:00:00", "notes": "Cooling fan for Server S203", "total_checked_in": 0, "remaining_quantity": 3, "is_fully_returned": false}, {"id": 10, "component": 5, "to_asset": {"id": 13, "displayed_id": "Desktop D301"}, "quantity": 2, "checkout_date": "2025-09-20T11:00:00", "notes": "Network upgrade for D301", "total_checked_in": 0, "remaining_quantity": 2, "is_fully_returned": false}, {"id": 11, "component": 5, "to_asset": {"id": 14, "displayed_id": "Desktop D302"}, "quantity": 3, "checkout_date": "2025-09-22T15:30:00", "notes": "Network upgrade for D302", "total_checked_in": 0, "remaining_quantity": 3, "is_fully_returned": false}, {"id": 12, "component": 6, "to_asset": {"id": 15, "displayed_id": "Workstation W401"}, "quantity": 4, "checkout_date": "2025-09-25T10:30:00", "notes": "GPU upgrade for W401", "total_checked_in": 0, "remaining_quantity": 4, "is_fully_returned": false}, {"id": 13, "component": 6, "to_asset": {"id": 16, "displayed_id": "Workstation W402"}, "quantity": 4, "checkout_date": "2025-09-26T14:00:00", "notes": "GPU upgrade for W402", "total_checked_in": 0, "remaining_quantity": 4, "is_fully_returned": false}, {"id": 14, "component": 7, "to_asset": {"id": 17, "displayed_id": "Desktop D501"}, "quantity": 2, "checkout_date": "2025-10-01T09:00:00", "notes": "Motherboard replacement for D501", "total_checked_in": 0, "remaining_quantity": 2, "is_fully_returned": false}, {"id": 15, "component": 7, "to_asset": {"id": 18, "displayed_id": "Desktop D502"}, "quantity": 2, "checkout_date": "2025-10-03T10:30:00", "notes": "Motherboard upgrade for D502", "total_checked_in": 0, "remaining_quantity": 2, "is_fully_returned": false}, {"id": 16, "component": 8, "to_asset": {"id": 19, "displayed_id": "Laptop L601"}, "quantity": 2, "checkout_date": "2025-10-05T11:00:00", "notes": "Battery replacement for L601", "total_checked_in": 0, "remaining_quantity": 2, "is_fully_returned": false}, {"id": 17, "component": 8, "to_asset": {"id": 20, "displayed_id": "Laptop L602"}, "quantity": 1, "checkout_date": "2025-10-06T14:00:00", "notes": "Battery replacement for L602", "total_checked_in": 0, "remaining_quantity": 1, "is_fully_returned": false}, {"id": 18, "component": 9, "to_asset": {"id": 21, "displayed_id": "Laptop L701"}, "quantity": 2, "checkout_date": "2025-10-07T09:30:00", "notes": "Docking station for L701", "total_checked_in": 0, "remaining_quantity": 2, "is_fully_returned": false}, {"id": 19, "component": 9, "to_asset": {"id": 22, "displayed_id": "Laptop L702"}, "quantity": 2, "checkout_date": "2025-10-08T10:15:00", "notes": "Docking station for L702", "total_checked_in": 0, "remaining_quantity": 2, "is_fully_returned": false}, {"id": 20, "component": 9, "to_asset": {"id": 23, "displayed_id": "Laptop L703"}, "quantity": 2, "checkout_date": "2025-10-09T14:00:00", "notes": "Docking station for L703", "total_checked_in": 0, "remaining_quantity": 2, "is_fully_returned": false}, {"id": 21, "component": 10, "to_asset": {"id": 24, "displayed_id": "Server Room R201"}, "quantity": 10, "checkout_date": "2025-10-01T08:00:00", "notes": "<PERSON><PERSON><PERSON> for R201", "total_checked_in": 0, "remaining_quantity": 10, "is_fully_returned": false}, {"id": 22, "component": 10, "to_asset": {"id": 25, "displayed_id": "Server Room R202"}, "quantity": 10, "checkout_date": "2025-10-02T09:30:00", "notes": "<PERSON><PERSON><PERSON> for R202", "total_checked_in": 0, "remaining_quantity": 10, "is_fully_returned": false}]