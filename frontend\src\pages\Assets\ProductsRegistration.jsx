import NavBar from '../../components/NavBar';
import '../../styles/Registration.css';
import { useNavigate, useParams } from 'react-router-dom';
import TopSecFormPage from '../../components/TopSecFormPage';
import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import CloseIcon from '../../assets/icons/close.svg';
import Alert from "../../components/Alert";
import assetsService from "../../services/assets-service";
import SystemLoading from "../../components/Loading/SystemLoading";


export default function ProductsRegistration() {
  const [suppliers, setSuppliers] = useState([]);
  const [manufacturers, setManufacturers] = useState([]);
  const [categories, setCategories] = useState([]);
  const [depreciations, setDepreciations] = useState([]);
  const [product, setProduct] = useState(null);



  const { id } = useParams();
  const { setValue, register, handleSubmit, watch, formState: { errors, isValid } } = useForm({
    mode: "all",
    defaultValues: {
      productName: '',
      category: '',
      manufacturer: '',
      depreciation: '',
      modelNumber: '',
      endOfLifeDate: '',
      defaultPurchaseCost: '',
      supplier: '',
      minimumQuantity: '',
      operatingSystem: '',
      notes: ''
    }
  });

  const currentDate = new Date().toISOString().split('T')[0];
  const [previewImage, setPreviewImage] = useState(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const [removeImage, setRemoveImage] = useState(false);

  const navigate = useNavigate();

  const [errorMessage, setErrorMessage] = useState("");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const initialize = async () => {
      try {
        setIsLoading(true);
        
        // Fetch all necessary data in parallel
        const [productContextsData, contextsData] = await Promise.all([
          assetsService.fetchProductContexts(),
          fetchAllCategories()
        ]);
        
        // Set categories and depreciations from product contexts
        setCategories(productContextsData.categories || []);
        setDepreciations(productContextsData.depreciations || []);
        
        // Set suppliers and manufacturers from contexts
        setSuppliers(contextsData.suppliers || []);
        setManufacturers(contextsData.manufacturers || []);
        
        console.log("Categories:", productContextsData.categories);
        console.log("Depreciations:", productContextsData.depreciations);
        console.log("Suppliers:", contextsData.suppliers);
        console.log("Manufacturers:", contextsData.manufacturers);

        // If ID is present, fetch the product details
        if (id) {
          const productData = await assetsService.fetchProductById(id);
          if (!productData) {
            setErrorMessage("Failed to fetch product details");
            setIsLoading(false);
            return;
          }

          setProduct(productData);
          console.log("Product Details:", productData);

          // Set form values from retrieved product data
          setValue('productName', productData.name);

          setValue('category', productData.category_id || '');
          setValue('depreciation', productData.depreciation_id || '');
          setValue('manufacturer', productData.manufacturer_id || '');
          setValue('modelNumber', productData.model_number || '');
          setValue('endOfLifeDate', productData.end_of_life || '');
          setValue('defaultPurchaseCost', productData.default_purchase_cost || '');
          setValue('supplier', productData.default_supplier_id || '');
          setValue('minimumQuantity', productData.minimum_quantity || '');
          setValue('operatingSystem', productData.operating_system || '');
          setValue('notes', productData.notes || '');
          
          if (productData.image) {
            setPreviewImage(`https://assets-service-production.up.railway.app${productData.image}`);
          }
        }
      } catch (error) {
        console.error("Error initializing:", error);
        setErrorMessage("Failed to initialize form data");
      } finally {
        setIsLoading(false);
      }
    };

    initialize();
  }, [id, setValue]);



  const handleImageSelection = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        setErrorMessage("Image size exceeds 5MB. Please choose a smaller file.");
        setTimeout(() => setErrorMessage(""), 5000);
        return;
      }
      
      setSelectedImage(file); // store the actual file
      setValue('image', file); // optional: sync with react-hook-form
  
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewImage(reader.result); // for display only
      };
      reader.readAsDataURL(file);
    }
  };  
  
  const onSubmit = async (data) => {
    try {
      // Only check for duplicate names when creating a new product (not when updating)
      if (!id) {
        // Fetch all existing product names
        const existingProducts = await assetsService.fetchProductNames();
        
        // Check if a product with the same name already exists
        const isDuplicate = existingProducts.products.some(
          product => product.name.toLowerCase() === data.productName.toLowerCase()
        );
        
        if (isDuplicate) {
          setErrorMessage("A product with this name already exists. Please use a different name.");
          setTimeout(() => {
            setErrorMessage("");
          }, 5000);
          return; // Stop the submission process
        }
      }

      const formData = new FormData();

      // Append all form data to FormData object
      formData.append('name', data.productName);
      formData.append('category', data.category || '');
      formData.append('manufacturer_id', data.manufacturer || '');
      formData.append('depreciation', data.depreciation || '');
      formData.append('model_number', data.modelNumber || '');
      formData.append('end_of_life', data.endOfLifeDate || '');
      formData.append('default_purchase_cost', data.defaultPurchaseCost || '');
      formData.append('default_supplier_id', data.supplier || '');
      formData.append('minimum_quantity', data.minimumQuantity);
      formData.append('operating_system', data.operatingSystem || '');
      formData.append('notes', data.notes || '');
      
      // Handle image upload
      if (selectedImage) {
        formData.append('image', selectedImage);
      }

      // Handle image removal
      if (removeImage) {
        formData.append('remove_image', 'true');
        console.log("Removing image: remove_image flag set to true");
      }
      
      console.log("Form data before submission:");
      for (let pair of formData.entries()) {
        console.log(pair[0] + ': ' + pair[1]);
      }
      
      let result;
      
      if (id) {
        // Update existing product
        result = await assetsService.updateProduct(id, formData);
      } else {
        // Create new product
        result = await assetsService.createProduct(formData);
      }

      if (!result) {
        throw new Error(`Failed to ${id ? 'update' : 'create'} product.`);
      }

      console.log(`${id ? 'Updated' : 'Created'} product:`, result);
      
      // Navigate to products page with success message
      navigate('/products', { 
        state: { 
          successMessage: `Product has been ${id ? 'updated' : 'created'} successfully!` 
        } 
      });
    } catch (error) {
      console.error(`Error ${id ? 'updating' : 'creating'} product:`, error);
      setErrorMessage(error.message || `An error occurred while ${id ? 'updating' : 'creating'} the product`);
    }
  };

  if (isLoading) {
    console.log("isLoading triggered — showing loading screen");
    return <SystemLoading />;
  }


  return (
    <>
      {errorMessage && <Alert message={errorMessage} type="danger" />}
      <nav><NavBar /></nav>
      <main className="registration">
        <section className="top">
          <TopSecFormPage
            root="Asset Models"
            currentPage={id ? "Edit Asset Model" : "New Asset Model"}
            rootNavigatePage="/products"
            title={id ? 'Edit' + ' ' + (product?.name || 'Asset Model') : 'New Asset Model'}
          />
        </section>
        <section className="registration-form">
          <form onSubmit={handleSubmit(onSubmit)}>

            {/* Asset Model Name */}
            <fieldset>
              <label htmlFor='product-name'>Asset Model Name <span style={{color: 'red'}}>*</span></label>
              <input
                type='text'
                className={errors.productName ? 'input-error' : ''}
                {...register('productName', { required: 'Asset Model Name is required' })}
                maxLength='100'
                placeholder='Asset Model Name'
              />
              {errors.productName && <span className='error-message'>{errors.productName.message}</span>}
            </fieldset>

            <fieldset>
              <label htmlFor='category'>Category <span style={{color: 'red'}}>*</span></label>
              <select
                id="category"
                {...register("category", { required: "Category is required" })}
                className={errors.category ? 'input-error' : ''}
              >
                <option value="">Select Category</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
              {errors.category && <span className='error-message'>{errors.category.message}</span>}
            </fieldset>

            <fieldset>
              <label htmlFor='manufacturer'>Manufacturer <span style={{color: 'red'}}>*</span></label>
              <select
                id="manufacturer"
                {...register("manufacturer", { required: "Manufacturer is required" })}
                className={errors.manufacturer ? 'input-error' : ''}
              >
                <option value="">Select Manufacturer</option>
                {manufacturers.map(manufacturer => (
                  <option key={manufacturer.id} value={manufacturer.id}>
                    {manufacturer.name}
                  </option>
                ))}
              </select>
              {errors.manufacturer && <span className='error-message'>{errors.manufacturer.message}</span>}
            </fieldset>

            <fieldset>
              <label htmlFor='depreciation'>Depreciation <span style={{color: 'red'}}>*</span></label>
              <select
                id="depreciation"
                {...register("depreciation", { required: "Depreciation is required" })}
                className={errors.depreciation ? 'input-error' : ''}
              >
                <option value="">Select Depreciation Method</option>
                {depreciations.map(depreciation => (
                  <option key={depreciation.id} value={depreciation.id}>
                    {depreciation.name}
                  </option>
                ))}
              </select>
              {errors.depreciation && <span className='error-message'>{errors.depreciation.message}</span>}
            </fieldset>

            <fieldset>
              <label htmlFor='supplier'>Default Supplier</label>
              <select
                id="supplier"
                {...register("supplier")}
              >
                <option value="">Select Supplier</option>
                {suppliers.map(supplier => (
                  <option key={supplier.id} value={supplier.id}>
                    {supplier.name}
                  </option>
                ))}
              </select>
            </fieldset>

            {/* Model Number */}
            <fieldset>
              <label htmlFor='model-number'>Model Number</label>
              <input 
                type='text'
                {...register('modelNumber')} 
                maxLength='100'
                placeholder='Model Number'
              />
            </fieldset>

            {/* End of Life Date */}
            <fieldset>
              <label htmlFor='end-of-life-date'>End of Life Date</label>
              <input
                type='date'
                {...register('endOfLifeDate')}
                min={!id ? currentDate : undefined}
              />
            </fieldset>

            <fieldset className="cost-field">
              <label htmlFor="defaultPurchaseCost">Default Purchase Cost</label>
              <div className="cost-input-group">
                <span className="cost-addon">PHP</span>
                <input
                  type="number"
                  id="defaultPurchaseCost"
                  name="defaultPurchaseCost"
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                  {...register("defaultPurchaseCost", { valueAsNumber: true })}
                />
              </div>
            </fieldset>

            <fieldset>
              <label htmlFor='minimum-quantity'>Minimum Quantity <span style={{color: 'red'}}>*</span></label>
              <input
                type='number'
                className={errors.minimumQuantity ? 'input-error' : ''}
                {...register('minimumQuantity', {
                  required: 'Minimum Quantity is required',
                  min: { value: 0, message: 'Minimum Quantity must be at least 0' },
                  valueAsNumber: true
                })}
                placeholder='Minimum Quantity'
                min="0"
              />
              {errors.minimumQuantity && <span className='error-message'>{errors.minimumQuantity.message}</span>}
            </fieldset>

            <fieldset>
              <label htmlFor='operatingSystem'>Operating System</label>
              <select
                id="operatingSystem"
                {...register("operatingSystem")}
              >
                <option value="">Select Operating System</option>
                <option value="linux">Linux</option>
                <option value="windows">Windows</option>
                <option value="macos">macOS</option>
                <option value="ubuntu">Ubuntu</option>
                <option value="centos">CentOS</option>
                <option value="debian">Debian</option>
                <option value="fedora">Fedora</option>
                <option value="other">Other</option>
              </select>
            </fieldset>

            <fieldset>
              <label htmlFor='notes'>Notes</label>
              <textarea 
                {...register('notes')} 
                maxLength='500'
                placeholder='Notes...'
              />
            </fieldset>

            <fieldset>
              <label>Image</label>
              {previewImage ? (
                <div className="image-selected">
                  <img src={previewImage} alt="Selected image" />
                  <button
                    type="button"
                    onClick={(event) => {
                      event.preventDefault();
                      setPreviewImage(null);
                      setSelectedImage(null);
                      setValue('image', null);
                      document.getElementById('image').value = '';
                      setRemoveImage(true);
                      console.log("Remove image flag set to:", true);
                    }}
                  >
                    <img src={CloseIcon} alt="Remove" />
                  </button>
                </div>
              ) : (
                <label className="upload-image-btn">
                  Choose File
                  <input
                    type="file"
                    id="image"
                    accept="image/*"
                    onChange={handleImageSelection}
                    style={{ display: "none" }}
                  />
                </label>
              )}
              <small className="file-size-info">
                Maximum file size must be 5MB
              </small>
            </fieldset>

            <button type='submit' className='primary-button' disabled={!isValid}>
              Save
            </button>
          </form>
        </section>
      </main>
    </>
  );
}
