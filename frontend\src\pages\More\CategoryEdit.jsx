import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import NavBar from "../../components/NavBar";
import TopSecFormPage from "../../components/TopSecFormPage";
import CloseIcon from "../../assets/icons/close.svg";
import Footer from "../../components/Footer";

import "../../styles/Registration.css";
import "../../styles/CategoryRegistration.css";

const CategoryEdit = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const [attachmentFile, setAttachmentFile] = useState(null);
  const [initialAttachment, setInitialAttachment] = useState(true);

  // Retrieve the "category" data value passed from the navigation state.
  // If the "category" data is not exist, the default value for this is "undifiend".
  const category = location.state?.category;

  console.log("attachment:", attachmentFile);

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
  } = useForm({
    defaultValues: {
      categoryName: category.name,
      categoryType: category.type.toLowerCase(),
    },
    mode: "all",
  });

  const categoryTypes = [
    "Asset",
    "Accessory",
    "Consumable",
    "Component",
    "License",
  ];

  const handleFileSelection = (e) => {
    if (e.target.files && e.target.files[0]) {
      // Check file size (max 5MB)
      if (e.target.files[0].size > 5 * 1024 * 1024) {
        alert("File size must be less than 5MB");
        e.target.value = "";
        return;
      }
      setAttachmentFile(e.target.files[0]);
    }
  };

  const onSubmit = (data) => {
    // Here you would typically send the data to your API
    console.log("Form submitted:", data, attachmentFile);

    // Optional: navigate back to categories view after successful submission
    navigate("/More/ViewCategories", { state: { updatedCategory: true } });
  };

  console.log("initial:", initialAttachment);

  return (
    <>
      <section className="page-layout-registration">
        <NavBar />
        <main className="registration">
          <section className="top">
            <TopSecFormPage
              root="Categories"
              currentPage="Edit Category"
              rootNavigatePage="/More/ViewCategories"
              title="Edit Category"
            />
          </section>
          <section className="registration-form">
            <form onSubmit={handleSubmit(onSubmit)}>
              <fieldset>
                <label htmlFor="categoryName">Category Name *</label>
                <input
                  type="text"
                  placeholder="Category Name"
                  maxLength="100"
                  className={errors.categoryName ? "input-error" : ""}
                  {...register("categoryName", {
                    required: "Category Name is required",
                  })}
                />
                {errors.categoryName && (
                  <span className="error-message">
                    {errors.categoryName.message}
                  </span>
                )}
              </fieldset>

              <fieldset>
                <label htmlFor="categoryType">Category Type *</label>
                <select
                  disabled
                  title="Cannot change category type"
                  className={errors.categoryType ? "input-error" : ""}
                  {...register("categoryType", {
                    required: "Category Type is required",
                  })}
                >
                  <option value="">Select Category Type</option>
                  {categoryTypes.map((type, idx) => (
                    <option key={idx} value={type.toLowerCase()}>
                      {type}
                    </option>
                  ))}
                </select>
                {errors.categoryType && (
                  <span className="error-message">
                    {errors.categoryType.message}
                  </span>
                )}
              </fieldset>

              <fieldset>
                <label>Icon</label>
                {attachmentFile || initialAttachment ? (
                  <div className="image-selected">
                    <img
                      // src={category.icon}
                      src={
                        initialAttachment
                          ? category.icon
                          : URL.createObjectURL(attachmentFile)
                      }
                      alt="Selected icon"
                    />
                    <button
                      type="button"
                      onClick={() => {
                        setAttachmentFile(null);
                        setInitialAttachment(false);
                      }}
                    >
                      <img src={CloseIcon} alt="Remove" />
                    </button>
                  </div>
                ) : (
                  <label className="upload-image-btn">
                    Choose File
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleFileSelection}
                      style={{ display: "none" }}
                    />
                  </label>
                )}
                <small className="file-size-info">
                  Maximum file size must be 5MB
                </small>
              </fieldset>

              <button
                type="submit"
                className="primary-button"
                disabled={!isValid}
              >
                Save
              </button>
            </form>
          </section>
        </main>
        <Footer />
      </section>
      {/* <nav>
        <NavBar />
      </nav> */}
    </>
  );
};

export default CategoryEdit;
