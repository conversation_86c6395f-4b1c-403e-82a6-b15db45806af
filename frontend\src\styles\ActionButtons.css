/* Base */
.action-button-section {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem; /* space between buttons */
}

.action-button {
  border: none;
  border: 1px solid #cccccc;
  background-color: #f9f9f9;
  padding: 5px 10px;
  border-radius: 4px;
  color: #555555;
  font-size: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease,
    border-color 0.2s ease;
}

.action-button:disabled {
  cursor: not-allowed;
  position: relative;
}

.action-button:disabled::after {
  content: "";
  width: 100%;
  height: 100%;

  background-color: #d3d3d367;
  border-radius: 4px;
  border: 1px solid #d3d3d356;

  position: absolute;
  top: -1px;
  left: -1px;
}

.action-button:hover:not(.action-button:hover:disabled) {
  background-color: #f0f0f0;
  color: #333333;
  border-color: #999999;
}

/* Check-In <PERSON><PERSON> (primary blue style - matches Components) */
.action-button.action-button-checkin {
  background-color: var(--primary-color) !important;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 100px;
  justify-content: center;
}

.action-button.action-button-checkin:hover {
  background-color: var(--primary-color-hover) !important;
}

/* Check-Out Button (green style - matches Components) */
.action-button.action-button-checkout {
  background-color: #34c759 !important;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 100px;
  justify-content: center;
}

.action-button.action-button-checkout:hover {
  background-color: rgba(52, 199, 89, 0.8) !important;
}

/* Make sure icons and text inside are white */
.action-button-checkin i,
.action-button-checkout i,
.action-button-checkin span,
.action-button-checkout span {
  color: #fff;
}

/* Aligns left inside its cell */
.action-button-recover,
.action-button-checkin,
.action-button-checkout {
  margin-right: auto;
}

.action-button:disabled,
.action-button.disabled {
  background-color: var(--disabled-color) !important;
  cursor: not-allowed;
  pointer-events: none;
}

.action-button:disabled:hover,
.action-button.disabled:hover {
  background-color: inherit !important;
}
