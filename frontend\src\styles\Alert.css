.alert {
  display: flex;
  flex-direction: row;
  justify-content: start;
  align-items: center;
  width: 35vw;
  padding: 12px 18px;
  border-radius: 10px;
  margin-bottom: 10px;
  gap: 12px;
  font-size: 1rem;
  font-weight: 500;
  position: absolute;
  z-index: 2;
  left: 50%;
  right: 50%;
  top: 100px;
  transform: translate(-50%, -50%);
  animation: fade-in-out 5s ease-in-out forwards;
}

.alert .alert-image {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 15px;
  height: 15px;
  padding: 3px;
  border-radius: 50%;
  object-fit: cover;
}

.alert-danger,
.alert-danger p {
  background-color: var(--warning-bg);
  color: var(--warning-text);
}

.alert-danger img {
  background-color: var(--warning-text);
  box-shadow: 0px 0px 0px 3px rgba(255, 62, 29, 0.25),
    0px 0px 0px 6px rgba(255, 62, 29, 0.15);
}

.alert-success,
.alert-success p {
  background-color: var(--success-bg);
  color: var(--success-text);
}

.alert-success img {
  background-color: var(--success-text);
  box-shadow: 0px 0px 0px 3px rgba(113, 221, 55, 0.25),
    0px 0px 0px 6px rgba(113, 221, 55, 0.15);
}

@keyframes fade-in-out {
  0% {
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
