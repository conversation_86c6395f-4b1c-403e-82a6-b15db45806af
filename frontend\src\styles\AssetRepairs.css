/* Asset Repairs page specific styles */
.page {
  display: flex;
  flex-direction: column;
  background-color: var(--bg-color);
  height: 100vh;
  width: 100vw;
  padding: 100px 38px 38px 38px;
  position: relative;
  z-index: 1;
}

.container {
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: center;
  height: auto;
  width: 100%;
  background-color: #ffffff;
  border-radius: 40px;
  border: 1px solid #d3d3d3;
  box-shadow: 0 0 20px rgba(211, 211, 211, 0.8);
  padding-bottom: 28px;
  overflow: hidden;
}

.top {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 28px 28px 0 28px;
}

.top h1 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: var(--secondary-text-color);
}

.top div {
  display: flex;
  align-items: center;
  gap: 10px;
}

.top input {
  padding: 0.5rem 1rem;
  border: 1px solid #d3d3d3;
  border-radius: 40px;
  font-size: 0.88rem;
  color: var(--secondary-text-color);
}

.top input:hover {
  border: 1px solid var(--primary-color);
  box-shadow: 0 0 10px rgba(0, 123, 255, 0.15);
}

.middle {
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: center;
  width: 100%;
  overflow-x: auto;
}

.page table {
  border-collapse: collapse;
  width: 100%;
  table-layout: fixed;
  border-radius: 0;
  overflow: hidden;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

.page table th {
  background-color: rgba(211, 211, 211, 0.2);
  height: 50px;
  vertical-align: middle;
  font-weight: 600;
  text-align: left;
}

.page table th,
.page table td {
  padding: 12px 16px;
  border-bottom: 1px solid #d3d3d3;
  font-size: 0.75rem;
  color: var(--secondary-text-color);
  text-align: left;
  overflow: visible;
  white-space: normal;
  position: relative;
  border-radius: 0;
}

.page table td {
  font-size: 0.88rem;
  color: var(--secondary-text-color);
  height: 60px;
  vertical-align: middle;
}

/* Add tooltip for text overflow */
.page table td[title] {
  cursor: default;
}

/* Set the width of the checkbox column */
.page table th:nth-child(1),
.page table td:nth-child(1) {
  width: 3vw;
  text-align: center;
}

/* Set the width of the asset column */
.page table th:nth-child(2) {
  width: 150px;
  text-align: left;
}

.page table td:nth-child(2) {
  width: 150px;
  text-align: left;
  color: #007bff;
}

/* Override any other styles that might be affecting the asset column */
.page table td.asset-column span {
  color: #007bff !important;
  text-align: left !important;
  display: block;
}

/* Set the width of the type column */
.page table th:nth-child(3),
.page table td:nth-child(3) {
  width: 100px;
  text-align: left;
}

/* Set the width of the name column */
.page table th:nth-child(4),
.page table td:nth-child(4) {
  width: 150px;
  text-align: left;
}

/* Set the width of the date columns */
.page table th:nth-child(5),
.page table td:nth-child(5),
.page table th:nth-child(6),
.page table td:nth-child(6) {
  width: 100px;
  text-align: left;
}

/* Set the width of the cost column */
.page table th:nth-child(7),
.page table td:nth-child(7) {
  width: 100px;
  text-align: left;
}

/* Set the width of the supplier column */
.page table th:nth-child(8),
.page table td:nth-child(8) {
  width: 100px;
  text-align: left;
}

/* Set the width of the notes column */
.page table th:nth-child(9),
.page table td:nth-child(9) {
  width: 100px;
  text-align: left;
}

/* Set the width of the attachments column */
.page table th:nth-child(10),
.page table td:nth-child(10) {
  width: 100px;
  text-align: left;
}

/* Set the width of the edit, view and delete columns */
.page table th:nth-child(11),
.page table td:nth-child(11),
.page table th:nth-child(12),
.page table td:nth-child(12),
.page table th:nth-child(13),
.page table td:nth-child(13) {
  width: 80px;
  text-align: center;
  vertical-align: middle;
  padding: 12px 8px;
}

/* Style the action buttons */
.page table td button {
  vertical-align: middle;
  margin: 0 auto;
}

/* Add specific styling for action buttons in Asset Repairs */
.page table td:nth-child(11) button,
.page table td:nth-child(12) button,
.page table td:nth-child(13) button {
  margin: 0 auto;
}

/* Create more space between action columns */
.page table th:nth-child(11),
.page table th:nth-child(12),
.page table th:nth-child(13),
.page table th.action-column {
  padding-left: 15px;
  padding-right: 15px;
}

/* Add specific styling for action column headers */
.page table th.action-column {
  min-width: 80px;
  width: 80px;
}

/* Add specific styling for action column cells */
.page table td.action-column {
  min-width: 80px;
  width: 80px;
  padding: 12px 20px;
}

/* Ensure icons in buttons are centered */
.page table td button svg {
  vertical-align: middle;
  display: inline-block;
}
