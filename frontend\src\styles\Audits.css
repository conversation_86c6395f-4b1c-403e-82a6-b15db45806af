.audit-title-page-section {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.audit-title-page-section h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
}

.audit-title-page-section > div {
  display: flex;
  align-items: center;
  gap: 10px;
}

.audit-table-section {
  overflow-x: auto;
  max-height: 500px;
  overflow-y: auto;


  table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;

    th {
      padding: 0.75rem !important;
      /* 12px */
      text-align: left !important;
      font-size: 0.75rem;
      /* 12px */
      font-weight: 600;
      color: var(--text-color);
      border-top: 1px solid #cccccc;
      border-bottom: 1px solid #cccccc;
      background-color: #f0f1f2;
      height: 50px;
      position: sticky;
      top: 0;
      z-index: 1;
    }

    td {
      text-align: left !important;
      padding: 0.75rem;
      font-size: 0.8125rem;
      /* 13px */
      color: var(--text-color);
      border-bottom: 1px solid #cccccc;

      height: 50px;
    }

    td.no-data-message {
      display: table-cell !important;
      width: 100% !important;
      white-space: normal !important;
      padding: 0.75rem !important;
      text-align: center !important;
      font-style: italic;
      color: var(--secondary-text-color);
      background: transparent;
    }

    tr:hover {
      background-color: #fcfcfc;
    }

    th:nth-child(-n+3),
    td:nth-child(-n+3) {
      width: calc((100% - 220px) / 3);
    }

    th:nth-child(4),
    td:nth-child(4) {
      width: 70px;
    }

    th:last-child,
    td:last-child {
      width: 150px;
    }
  }
}