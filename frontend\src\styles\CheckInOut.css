.check-in-out-page {
  display: flex;
  flex-direction: column;
  height: auto;
  width: auto;
  padding: 80px 38px 38px 38px;
  background-color: var(--bg-color);
}

.check-in-out-page .top {
  display: flex;
}

.check-in-out-page .middle {
  display: flex;
  flex-direction: row;
  width: 100%;
  gap: 20px;
}

.check-in-out-page .recent-checkout-info fieldset {
  display: flex;
  flex-direction: column;
}

.check-in-out-page .recent-checkout-info h2 {
  font-size: 1.25rem;
  color: var(--secondary-text-color);
}

.check-in-out-page .recent-checkout-info p {
  display: flex;
  color: var(--secondary-text-color);
  width: 100%;
  overflow-wrap: anywhere;
}

.check-in-out-page .checkin-form,
.check-in-out-page .recent-checkout-info {
  display: flex;
  flex-direction: column;
  height: auto;
  width: 100%;
  align-self: flex-start;
  padding: 24px;
  margin-top: 20px;
  background-color: #ffffff;
  box-shadow: 0 0 20px rgba(211, 211, 211, 0.3);
  border-radius: 40px;
  border: 1px solid #d3d3d3;
}

.check-in-out-page .recent-checkout-info {
  display: flex;
  align-self: flex-start;
  width: 35%;
  background-color: var(--bg-color);
  box-shadow: none;
}

.check-in-out-page form {
  display: flex;
  width: 100%;
  flex-direction: column;
  gap: 20px;
}

.check-in-out-page form fieldset {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.check-in-out-page fieldset label:not(.upload-image-btn) {
  color: var(--secondary-text-color);
  font-weight: 600;
}

.check-in-out-page select,
.check-in-out-page textarea {
  width: 100%;
  padding: 13px 16px;
  border-radius: 10px;
  border: 1px solid #d3d3d3;
  justify-content: flex-start;
}

.check-in-out-page form fieldset:nth-child(1) div {
  display: flex;
  flex-direction: row;
  gap: 20px;
  cursor: pointer;
  justify-content: flex-start;
}

.check-in-out-page input,
.check-in-out-page select,
.check-in-out-page textarea {
  width: 100%;
  padding: 13px 16px;
  border-radius: 10px;
  border: 1px solid #d3d3d3;
}

.check-in-out-page textarea {
  height: 13vh;
}

.check-in-out-page .save-btn {
  justify-content: center;
  align-items: center;
  height: 48px;
  padding: 12px 16px;
  border-radius: 40px;
  background-color: var(--primary-color);
  color: var(--bg-color);
  transition: 0.5s ease;
  cursor: pointer;
}

.check-in-out-page .save-btn:hover {
  background-color: var(--primary-color-hover);
}

.check-in-out-page .images-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 10px;
}

.check-in-out-page .image-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  width: 100px;
  border-radius: 10px;
  position: relative;
}

.check-in-out-page .image-selected imFg:not(.image-selected button img) {
  display: flex;
  height: 100%;
  width: 100%;
  object-fit: cover;
  border-radius: 15px;
  padding: 2px;
}

.check-in-out-page .image-selected button {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 20px;
  width: 20px;
  padding: 10px;
  border-radius: 50px;
  position: absolute;
  top: 0;
  right: 0;
  background-color: red;
  border: 2px solid #ffffff;
  cursor: pointer;
}

.check-in-out-page .image-selected button:hover {
  background-color: darkred;
}

.check-in-out-page .upload-image-btn {
  display: flex;
  width: fit-content;
  justify-content: center;
  align-items: center;
  background-color: var(--primary-color);
  border-radius: 40px;
  padding: 12px;
  font-size: 0.83333rem;
  color: var(--bg-color);
  cursor: pointer;
  transition: 0.5s ease;
}

.check-in-out-page .upload-image-btn:hover {
  background-color: var(--primary-color-hover);
}

.checkout-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: auto;
  padding: 100px 38px 38px 38px;
  background-color: var(--bg-color);
}

/* Radio button container styles */
.employee-radio-container,
.location-radio-container {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  border-radius: 10px;
  border: 1px solid #d3d3d3;
  cursor: pointer;
}

.employee-radio-container label,
.location-radio-container label {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  width: 100%;
}

.employee-radio-container input[type="radio"],
.location-radio-container input[type="radio"] {
  margin: 0;
  width: auto;
}

.item-info-image {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 250px;
  width: 250px;
  border-radius: 10px;
  position: relative;
}