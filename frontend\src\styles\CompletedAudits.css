.completed-audits-page {
  display: flex;
  flex-direction: column;
  background-color: var(--bg-color);
  height: 100vh;
  width: 100vw;
  padding: 100px 38px 38px 38px;
  position: relative;
  z-index: 1; /* Lower z-index to ensure it doesn't appear in front of overlays */
}

.completed-audits-page section {
  display: flex;
  flex-direction: column;
}

.completed-audits-page .main-top {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.completed-audits-page
  .main-top
  div:not(
    .status-archived,
    .status-deployed,
    .status-undeployable,
    .status-pending,
    .status-deployable,
    .status-lost
  ),
.completed-audits-page
  .container
  div:not(
    .status-archived,
    .status-deployed,
    .status-undeployable,
    .status-pending,
    .status-deployable,
    .status-lost
  ) {
  display: flex;
  align-items: center;
  gap: 10px;
}

.completed-audits-page .main-top button {
  padding: 12px 16px;
  border-radius: 40px;
  background-color: var(--primary-color);
  color: var(--bg-color);
  font-size: 1rem;
  cursor: pointer;
  transition: 0.5s ease;
}

.completed-audits-page .main-top button:hover {
  background-color: var(--primary-color-hover);
}

.completed-audits-page .tab-nav {
  width: 100%;
  border-bottom: 1px solid #d3d3d3;
}

.completed-audits-page .tab-nav ul {
  display: flex;
  flex-direction: row;
  gap: 20px;
  list-style-type: none;
}

.completed-audits-page li {
  padding: 13px 16px;
}

.completed-audits-page li.active {
  border-bottom: 3px solid var(--primary-color);
}

.completed-audits-page ul a {
  color: var(--secondary-text-color);
}

.completed-audits-page ul a:hover {
  color: var(--primary-color);
  cursor: pointer;
}

.completed-audits-page li a.active {
  color: var(--primary-color);
}

.completed-audits-page .container {
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: center;
  height: auto;
  width: 100%;
  background-color: #ffffff;
  border-radius: 20px;
  border: 1px solid #d3d3d3;
  box-shadow: 0 0 20px rgba(211, 211, 211, 0.8);
  padding-bottom: 28px;
  overflow: none;
  margin-top: 20px;
}

.completed-audits-page .top {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 16px 34px;
  border-bottom: 1px solid #d3d3d3;
  border-collapse: collapse;
}

.completed-audits-page .top h2 {
  font-size: 1.25rem;
}

.completed-audits-page .top input {
  width: 100%;
  padding: 12px 16px;
  border-radius: 40px;
  border: 1px solid #d3d3d3;
}

.completed-audits-page .top input:hover {
  border: 1px solid var(--primary-color);
  box-shadow: 0 0 10px rgba(0, 123, 255, 0.15);
}

.completed-audits-page table {
  border-collapse: collapse;
  width: 100%;
  table-layout: fixed;
}

.completed-audits-page table th {
  background-color: rgba(211, 211, 211, 0.2);
  color: var(--secondary-text-color) !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
}

.completed-audits-page table th,
.completed-audits-page table td {
  padding: 0.75rem;
  border-bottom: 1px solid #d3d3d3;
  font-size: 0.75rem;
  color: var(--secondary-text-color);
  text-align: left;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.completed-audits-page table td {
  font-size: 0.88rem;
  color: var(--secondary-text-color);
}

.completed-audits-page span {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 10px;
  color: #34c759;
}

/* Set the width of the checkbox header and its data*/
.completed-audits-page table th:nth-child(1),
.completed-audits-page table td:nth-child(1) {
  width: 3vw;
}

/* Set the width of the audit date, location and perform by header and its data */
.completed-audits-page table th:nth-child(2),
.completed-audits-page table td:nth-child(2),
.completed-audits-page table th:nth-last-child(3),
.completed-audits-page table td:nth-last-child(3),
.completed-audits-page table th:nth-last-child(4),
.completed-audits-page table td:nth-last-child(4) {
  width: 12vw;
}

/* Set the width of the asset head and its data */
.completed-audits-page table th:nth-child(3),
.completed-audits-page table td:nth-child(3) {
  width: 16vw;
}

/* Set the width of the status column */
.completed-audits-page table th:nth-child(4),
.completed-audits-page table td:nth-child(4) {
  width: 12vw;
}

/* Set the width of the location and perform by header and its data */
.completed-audits-page table th:nth-child(5),
.completed-audits-page table td:nth-child(5),
.completed-audits-page table th:nth-child(6),
.completed-audits-page table td:nth-child(6) {
  width: 13vw;
}

/* Set the width of the delete and view header and its data*/
.completed-audits-page table th:nth-last-child(1),
.completed-audits-page table td:nth-last-child(1) {
  /* width: 5vw; */
  width: 50px;
  text-align: center;
  padding-left: 0;
  padding-right: 0;
}

/* Center the action buttons */
.completed-audits-page table td:nth-last-child(1) button,
.completed-audits-page table td:nth-last-child(2) button {
  margin: 0 auto;
  display: block;
}
