.consumables-page {
  width: 100%;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.content-container {
  padding: 20px;
  margin: 0 auto;
  max-width: 1400px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin-top: 4rem; /* Create space between navbar and content */
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.action-buttons button {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  border: 1px solid #ddd;
  background-color: #fff;
}

.action-buttons .add-btn {
  background-color: #1a73e8;
  color: white;
  border: none;
}

.consumables-table {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 20px;
}

/* Make sure inner table has no rounded corners */
.consumables-table table {
  width: 100%;
  border-collapse: collapse;
  border-radius: 0;
}

/* Explicitly reset border-radius on table cells */
.consumables-table table th,
.consumables-table table td {
  border-radius: 0;
}

thead {
  background-color: #f8f9fa;
}

th, td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #eee;
  font-size: 0.875rem;
}

th {
  font-weight: 600;
  color: #555;
  text-transform: uppercase;
  font-size: 0.75rem;
}

.checkbox-col {
  width: 40px;
}

.image-col {
  width: 80px;
}

.item-image {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.item-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

/* Remove border-radius from action buttons */
.edit-btn, .view-btn, .delete-btn {
  width: 30px;
  height: 30px;
  border-radius: 0;
  border: 1px solid #ddd;
  background-color: #fff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Use unicode characters for icons if you don't have an icon library */
.edit-icon::before {
  content: "✏️";
}

.view-icon::before {
  content: "👁️";
}

.delete-icon::before {
  content: "🗑️";
}

/* Specific styles for action columns */
.consumables-page table td:nth-child(7),
.consumables-page table td:nth-child(8),
.consumables-page table td:nth-child(9) {
  padding: 4px !important;
  width: 40px !important;
  text-align: center !important;
  vertical-align: middle !important;
  height: 40px !important;
  border-radius: 0 !important;
}

.consumables-page table th:nth-child(7),
.consumables-page table th:nth-child(8),
.consumables-page table th:nth-child(9) {
  width: 40px !important;
  padding: 4px !important;
  text-align: center !important;
  border-radius: 0 !important;
}

/* Remove border-radius from action column buttons */
.consumables-page table td:nth-child(7) button,
.consumables-page table td:nth-child(8) button,
.consumables-page table td:nth-child(9) button {
  border-radius: 0 !important;
}

/* Pagination styles */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
}

.items-per-page {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: #555;
}

.items-per-page select {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.page-navigation {
  display: flex;
  align-items: center;
  gap: 8px;
}

.prev-btn, .next-btn {
  padding: 4px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
  cursor: pointer;
}

.page-number {
  padding: 4px 8px;
  background-color: #1a73e8;
  color: white;
  border-radius: 4px;
}