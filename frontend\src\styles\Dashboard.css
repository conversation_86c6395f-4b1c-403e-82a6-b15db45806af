.dashboard-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.dashboard-content {
  padding: 2rem;
  margin-top: 60px;
  flex-grow: 1;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
  width: 100%;
}

.dashboard-content h1 {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 1.5rem;
}

.status-cards-grid {
  display: grid;
  grid-template-columns: repeat(4, minmax(240px, 1fr)) minmax(240px, 1fr);
  grid-template-rows: repeat(2, 100px);
  gap: 1.5rem;
  margin-bottom: 2rem;
  position: relative;
  width: 100%;
}

.card-position-0,
.card-position-1,
.card-position-2,
.card-position-3,
.card-position-4,
.card-position-5,
.card-position-6,
.card-position-7,
.card-position-8 {
  position: relative;
  width: 100%;
  height: 100%;
}

.card-position-0 { grid-column: 1; grid-row: 1; } /* Due for Return */
.card-position-1 { grid-column: 2; grid-row: 1; } /* Upcoming Audits */
.card-position-2 { grid-column: 3; grid-row: 1; } /* Upcoming End of Life */
.card-position-3 { grid-column: 4; grid-row: 1; } /* Expiring Warranties */
.card-position-4 { grid-column: 1; grid-row: 2; } /* Overdue for Return */
.card-position-5 { grid-column: 2; grid-row: 2; } /* Overdue Audits */
.card-position-6 { grid-column: 3; grid-row: 2; } /* Reached End of Life */
.card-position-7 { grid-column: 4; grid-row: 2; } /* Expired Warranties */
.card-position-8 { 
  grid-column: 5;
  grid-row: 1 / span 2;
} /* Low Stock */

.status-card {
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 40px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

/* Responsive Grid */
@media (max-width: 1400px) {
  .dashboard-content {
    padding: 1.5rem;
  }

  .status-cards-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    justify-content: center;
  }

  .card-position-8 {
    grid-column: 1 / -1;
    grid-row: 3;
    height: 100px;
  }
}

@media (max-width: 1200px) {
  .status-cards-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .card-position-8 {
    grid-column: 1 / -1;
    grid-row: 4;
  }
}

@media (max-width: 992px) {
  .status-cards-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .card-position-8 {
    grid-column: 1 / -1;
    grid-row: 5;
  }
}

@media (max-width: 576px) {
  .dashboard-content {
    padding: 1rem;
  }

  .status-cards-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .card-position-8 {
    grid-column: 1;
    grid-row: 9;
  }
}