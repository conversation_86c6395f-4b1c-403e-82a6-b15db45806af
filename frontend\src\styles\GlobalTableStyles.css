/* Global table styles */
table th:nth-last-child(1),
table th:nth-last-child(2),
table th:nth-last-child(3) {
  text-align: center !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
  font-size: 0.7rem !important;
  letter-spacing: -0.5px !important;
}

table td:nth-last-child(1),
table td:nth-last-child(2),
table td:nth-last-child(3) {
  text-align: center !important;
  vertical-align: middle !important;
  padding: 8px !important;
}

.depreciation-table th:nth-last-child(1),
.depreciation-table th:nth-last-child(2),
.depreciation-table th:nth-last-child(3) {
  padding-left: 15px !important;
  padding-right: 15px !important;
  min-width: 80px !important;
}

.depreciation-table td:nth-last-child(1),
.depreciation-table td:nth-last-child(2),
.depreciation-table td:nth-last-child(3) {
  padding-left: 15px !important;
  padding-right: 15px !important;
  min-width: 80px !important;
}

table th:last-child,
table td:last-child {
  padding-right: 25px !important;
}

.email-cell,
td[style*="email"],
th:contains("EMAIL") {
  max-width: 150px !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  padding-right: 15px !important;
  padding-left: 0 !important;
}

.url-cell,
td[style*="url"],
th:contains("URL") {
  max-width: 150px !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  padding-right: 15px !important;
}

.email-cell, .url-cell {
  position: relative !important;
}

table td:nth-last-child(1) button:not(.action-button),
table td:nth-last-child(2) button:not(.action-button) button:not(.table-buttons-audit),
table td:nth-last-child(3) button:not(.action-button) {
  margin: 0 auto !important;
  display: block !important;
}

table input[type="checkbox"] {
  appearance: auto !important;
  -webkit-appearance: checkbox !important;
  -moz-appearance: checkbox !important;
  width: 16px;
  height: 16px;
  border: 1px solid #E5E7EB;
  border-radius: 0;
  cursor: pointer;
  margin: 0;
  position: relative;
  content: none !important;
}

table input[type="checkbox"]::before,
table input[type="checkbox"]::after {
  display: none !important;
  content: none !important;
}

.eol-table input[type="checkbox"] {
  appearance: auto !important;
  -webkit-appearance: checkbox !important;
  -moz-appearance: checkbox !important;
  content: none !important;
}

table td:first-child {
  position: relative;
}

table td:first-child::before,
table td:first-child::after {
  display: none !important;
  content: none !important;
}

.eol-container .eol-table input[type="checkbox"],
.eol-container table input[type="checkbox"] {
  appearance: auto !important;
  -webkit-appearance: checkbox !important;
  -moz-appearance: checkbox !important;
  content: none !important;
  font-size: 0 !important;
}

.eol-container .eol-table td:first-child,
.eol-container table td:first-child {
  font-size: 0 !important;
}


.eol-container .eol-table td:first-child input[type="checkbox"],
.eol-container table td:first-child input[type="checkbox"] {
  font-size: initial !important;
}


.eol-container input[type="checkbox"],
[class*="warranties"] input[type="checkbox"] {
  appearance: auto !important;
  -webkit-appearance: checkbox !important;
  -moz-appearance: checkbox !important;
  content: none !important;
  font-size: 0 !important;
}

.page table td:nth-child(9) .view-btn {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  background-color: #007bff !important;
  color: white !important;
  border: none !important;
  padding: 4px 8px !important;
  border-radius: 4px !important;
  cursor: pointer !important;
  font-size: 12px !important;
  min-width: 50px !important;
  text-align: center !important;
  margin: 0 auto !important;
  height: 26px !important;
}
