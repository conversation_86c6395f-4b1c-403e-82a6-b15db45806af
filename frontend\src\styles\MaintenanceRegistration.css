.maintenance-page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.maintenance-page-content {
  padding: 0 24px;
  margin: 0 auto;
  max-width: 100%;
  width: 100%;
  margin-top: 4rem;
}

.breadcrumb {
  display: flex;
  align-items: center;
  margin: 16px 0;
  font-size: 14px;
}

.root-link {
  color: #0066cc;
  cursor: pointer;
}

.root-link:hover {
  text-decoration: underline;
}

.separator {
  margin: 0 8px;
  color: #666;
}

.current-page {
  color: #333;
}

.page-title {
  font-size: 24px;
  font-weight: 500;
  margin: 20px 0;
  color: #333;
}

.form-container {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05); /* light stroke */
  padding: 24px;
  margin: 0 auto;
  max-width: 800px;
  margin-bottom: 32px;
  width: 100%;
}

.form-field {
  margin-bottom: 20px;
  width: 100%;
}

.form-field label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #333;
}

.form-field input,
.form-field select,
.form-field textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 10px;
  font-size: 14px;
  color: #333;
  box-sizing: border-box;
}

.form-field textarea {
  resize: vertical;
}

.select-wrapper {
  position: relative;
  width: 100%;
}

.select-wrapper select {
  appearance: none;
  padding-right: 30px;
  width: 100%;
}

.dropdown-arrow {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #666;
}

.date-picker-wrapper {
  position: relative;
  width: 100%;
}

.calendar-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

.cost-input {
  display: flex;
  align-items: center;
  width: 100%;
}

.currency {
  background-color: #f5f5f5;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-right: none;
  border-radius: 10px 0 0 10px;
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}

.cost-input input {
  border-radius: 0 10px 10px 0;
  flex-grow: 1;
}

.attachments-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
  flex-wrap: nowrap;
  width: 100%;
}

.choose-file-btn {
  background-color: #007bff;
  color: white;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  white-space: nowrap;
  flex-shrink: 0;
}

.choose-file-btn:hover {
  background-color: #0069d9;
}

.no-file {
  color: #666;
  font-size: 14px;
  margin-left: 8px;
  flex-grow: 1;
}

.file-size-limit {
  width: 100%;
  margin-top: 8px;
  color: #666;
  font-size: 12px;
  display: block;
}

.file-selected {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f8f9fa;
  padding: 8px 12px;
  border-radius: 4px;
  margin-left: 8px;
  width: auto;
  flex-grow: 1;
  max-width: calc(100% - 120px);
}

.file-selected p {
  margin: 0;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.remove-file-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  margin-left: 8px;
  flex-shrink: 0;
}

.remove-file-btn img {
  width: 14px;
  height: 14px;
}

.form-actions {
  margin-top: 24px;
  display: flex;
  justify-content: center;
  width: 100%;
}

/* Make sure the navbar doesn't overlap content */
nav {
  width: 100%;
  z-index: 100;
}

/* Responsive adjustments */
@media (min-width: 768px) {
  .form-container {
    padding: 32px;
  }

  .save-btn {
    max-width: 100%;
  }

  /* Add these styles to your MaintenanceRegistration.css file */

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
  }

  .delete-btn {
    background-color: #868686;
    color: #e6e6e6;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .delete-btn:hover {
    background-color: #ff6d62;
    color: #ffeeee;
  }

  .delete-btn i {
    font-size: 16px;
  }

  .selected-field {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    height: 40px;
    background-color: #f9f9f9;
  }

  .clear-selection {
    background: none;
    border: none;
    color: #999;
    font-size: 18px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    margin-left: 8px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
  }

  .clear-selection:hover {
    background-color: #f0f0f0;
    color: #666;
  }
}
