.manage-profile-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 80px;
}

.manage-profile-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.manage-profile-container h1 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 30px;
  margin-left: 20px;
}

.profile-content {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 60px;
  align-items: stretch;
}

/* Left Profile Card */
.profile-left {
  position: sticky;
  top: 100px;
  height: fit-content;
}

.profile-card {
  background: white;
  border-radius: 8px;
  padding: 25px 30px 30px 30px;
  box-shadow: 0 0 20px rgba(211, 211, 211, 0.8);
  border: 1px solid #d3d3d3;
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: calc(100vh - 200px);
}

.profile-image-section {
  margin-bottom: 25px;
  margin-top: 5px;
  flex-shrink: 0;
}

.profile-image-container {
  width: 120px;
  height: 120px;
  margin: 0 auto 15px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #e0e0e0;
}

.profile-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.change-image-btn {
  display: inline-block;
  padding: 8px 16px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 8px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.change-image-btn:hover {
  background-color: var(--primary-color-hover);
}

.profile-info {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.profile-info h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 15px;
}

.profile-details {
  text-align: left;
}

.profile-details p {
  margin: 5px 0;
  font-size: 13px;
  color: var(--secondary-text-color);
}

.profile-details p strong {
  color: var(--text-color);
}

/* Right Profile Settings */
.profile-right {
  display: flex;
  flex-direction: column;
  gap: 80px;
}

.profile-settings-card,
.authentication-card {
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 0 20px rgba(211, 211, 211, 0.8);
  border: 1px solid #d3d3d3;
}

.profile-settings-card h2,
.authentication-card h2 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 25px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 8px;
}

.form-group input {
  padding: 12px 16px;
  border: 1px solid #d3d3d3;
  border-radius: 8px;
  font-size: 13px;
  color: var(--text-color);
  background-color: white;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 10px rgba(0, 123, 255, 0.15);
}

.form-group input::placeholder {
  color: #999;
}

.save-changes-btn {
  width: 200px;
  padding: 12px 24px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-left: auto;
  display: block;
}

.save-changes-btn:hover {
  background-color: var(--primary-color-hover);
}

/* Authentication Details - Single Column */
.authentication-card {
  margin-top: 20px;
}

.authentication-card .form-group {
  margin-bottom: 20px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .profile-content {
    grid-template-columns: 280px 1fr;
    gap: 40px;
  }

  .profile-card {
    min-height: calc(100vh - 220px);
  }
}

@media (max-width: 992px) {
  .profile-content {
    grid-template-columns: 1fr;
    gap: 40px;
    align-items: start;
  }

  .profile-left {
    position: static;
    height: auto;
  }

  .profile-card {
    height: auto;
    min-height: auto;
  }

  .profile-right {
    gap: 40px;
    height: auto;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .manage-profile-container {
    padding: 15px;
  }

  .profile-right {
    gap: 40px;
  }

  .profile-card,
  .profile-settings-card,
  .authentication-card {
    padding: 20px;
    border-radius: 8px;
  }

  .manage-profile-container h1 {
    margin-left: 0;
    font-size: 20px;
  }
}
