.manufacturer-page-table-section {
    overflow-x: auto;
    max-height: 55vh;
    overflow-y: auto;

    /* Prevent affecting the parent when scrolling. */
    overscroll-behavior: contain;

    table {
        width: 100%;
        border-collapse: collapse;

        th {
            padding: 0.75rem !important;
            text-align: left !important;
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--text-color);
            border-top: 1px solid #cccccc;
            border-bottom: 1px solid #cccccc;
            background-color: #f0f1f2;

            height: 50px;

            position: sticky;
            top: 0;
            z-index: 1;
        }

        /* Checkbox Column */
        th:first-child,
        td:first-child {
            width: 10px;
        }

        /* Name Column */
        th:nth-child(2),
        td:nth-child(2) {
            min-width: 150px;
            max-width: 200px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .manufacturer-name span {
            width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        /* URL and Support URL Column */
        th:nth-child(3),
        td:nth-child(3),
        th:nth-child(4),
        td:nth-child(4) {
            min-width: 150px;
            max-width: 180px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Phone Number Column */
        th:nth-child(5),
        td:nth-child(5) {
            min-width: 100px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Email Column */
        th:nth-child(6),
        td:nth-child(6) {
            min-width: 150px;
            max-width: 180px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Notes Column */
        th:nth-child(7),
        td:nth-child(7) {
            min-width: 100px;
            max-width: 130px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Action Button Column */
        th:last-child,
        td:last-child {
            width: 50px;
        }

        td {
            text-align: left !important;
            padding: 0.75rem;
            font-size: 0.8125rem;
            color: var(--text-color);
            border-bottom: 1px solid #cccccc;

            height: 50px;
        }

        /* Ensure empty-state row spans correctly across all columns */
        td.no-data-message {
            display: table-cell !important;
            width: 100% !important;
            white-space: normal !important;
            padding: 0.75rem !important;
            text-align: center !important;
            font-style: italic;
            color: var(--secondary-text-color);
            background: transparent;
        }

        tr:hover {
            background-color: #fcfcfc;
        }

        img {
            width: 22px;
            height: 22px;
        }

        .action-button-section,
        .category-name {
            display: flex;
            flex-direction: row;
            align-items: center;
            color: var(--text-color);
            gap: 10px;
        }

        .action-button {
            border: none;
            border: 1px solid #cccccc;
            background-color: #f9f9f9;
            padding: 5px 10px;
            border-radius: 4px;
            color: #555555;
            font-size: 0.75rem;
            cursor: pointer;
            transition: background-color 0.2s ease, color 0.2s ease,
                border-color 0.2s ease;
        }

        .action-button:disabled {
            cursor: not-allowed;
            position: relative;
        }

        .action-button:disabled::after {
            content: "";
            width: 100%;
            height: 100%;

            background-color: #d3d3d367;
            border-radius: 4px;
            border: 1px solid #d3d3d356;

            position: absolute;
            top: -1px;
            left: -1px;
        }

        .action-button:hover:not(.action-button:hover:disabled) {
            background-color: #f0f0f0;
            color: #333333;
            border-color: #999999;
        }
    }
}

.table-pagination {
    padding: 0 20px;
}