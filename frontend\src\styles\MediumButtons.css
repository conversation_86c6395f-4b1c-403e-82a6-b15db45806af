.medium-button-new,
.medium-button-export,
.medium-button-schedule-audits,
.medium-button-perform-audits,
.medium-button-delete,
.medium-button-sort,
.medium-button-filter,
.medium-button-recover,
.medium-button-bulk {
  display: flex;
  height: 40px;
  justify-content: center;
  align-items: center;
  padding: 12px 16px;
  border-radius: 40px;
  gap: 5px;
  cursor: pointer;
  text-transform: capitalize;
  transition: 0.5s ease;
}

.medium-button-new {
  background-color: var(--primary-color);
  color: var(--bg-color);
}

.medium-button-new img,
.medium-button-delete img,
.medium-button-recover img {
  height: 16px !important;
  width: 16px !important;
}

.medium-button-new:hover {
  background-color: var(--primary-color-hover);
}

.medium-button-recover {
  background-color: var(--success-color);
  color: var(--bg-color);
}

.medium-button-recover:hover {
  background-color: var(--success-color-hover);
}


.medium-button-export,
.medium-button-sort,
.medium-button-filter {
  background-color: transparent;
  border: 1px solid #d3d3d3;
  color: var(--text-color);
}

.medium-button-export:hover,
.medium-button-sort:hover,
.medium-button-filter:hover {
  background-color: var(--primary-color-hover);
  color: var(--bg-color);
  border: 1px solid var(--primary-color-hover);
}

.medium-button-schedule-audits,
.medium-button-perform-audits,
.medium-button-bulk {
  height: auto;
  background-color: var(--primary-color);
  color: var(--bg-color);
  font-size: 1rem;
}

.medium-button-schedule-audits:hover,
.medium-button-perform-audits:hover,
.medium-button-bulk:hover {
  background-color: var(--primary-color-hover);
}

.medium-button-delete {
  background-color: red;
  color: var(--bg-color);
  transition: 0.5s ease;
}

.medium-button-delete:hover {
  background-color: darkred;
}

