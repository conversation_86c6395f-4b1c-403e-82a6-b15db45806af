/* Modal Overlay */
.modal-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 9999 !important;
  overflow: auto !important;
  padding: 20px !important;
  box-sizing: border-box !important;
}

.modal-container {
  background-color: white;
  border-radius: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 550px !important;
  min-width: 400px !important;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: relative;
}

.modal-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 24px 32px 20px 32px !important;
  border-bottom: 1px solid #e0e0e0 !important;
  margin: 0 !important;
  background: none !important;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
}

.modal-close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.modal-close-btn:hover {
  background-color: #f5f5f5;
}

.modal-close-btn img {
  width: 16px;
  height: 16px;
}

.modal-form {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.modal-body {
  padding: 24px 32px !important;
  flex: 1;
  overflow-y: auto;
}

.modal-body fieldset {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 32px;
  border: none;
  padding: 0;
}

.modal-body label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--secondary-text-color);
}

.modal-body input,
.modal-body select,
.modal-body textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d3d3d3;
  border-radius: 25px;
  font-size: 0.875rem;
  outline: none;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.modal-body input:focus,
.modal-body select:focus,
.modal-body textarea:focus {
  border-color: var(--primary-color);
}

.modal-body select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
}

.modal-body textarea {
  resize: vertical;
  min-height: 80px;
}

.input-error {
  border-color: #dc3545 !important;
}

.error-message {
  color: #dc3545;
  font-size: 0.75rem;
  margin-top: 4px;
}

.modal-info {
  font-size: 0.875rem;
  color: #666;
  margin-top: 16px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.link-text {
  color: var(--primary-color);
  font-weight: 500;
}

.modal-footer {
  display: flex !important;
  justify-content: flex-end !important;
  gap: 16px !important;
  padding: 20px 32px 24px 32px !important;
  border-top: 1px solid #e0e0e0 !important;
  background-color: #fafafa !important;
}

.modal-cancel-btn,
.modal-save-btn {
  padding: 10px 20px;
  border-radius: 25px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  min-width: 80px;
}

.modal-cancel-btn {
  background-color: #f8f9fa;
  color: #666;
  border: 1px solid #d3d3d3;
}

.modal-cancel-btn:hover {
  background-color: #e9ecef;
}

.modal-save-btn {
  background-color: var(--primary-color);
  color: white;
}

.modal-save-btn:hover:not(:disabled) {
  background-color: var(--primary-color-hover);
}

.modal-save-btn:disabled {
  background-color: #d3d3d3;
  cursor: not-allowed;
}

body.modal-open {
  overflow: hidden;
}

@media (max-width: 768px) {
  .modal-overlay {
    padding: 10px;
  }

  .modal-container {
    width: 95%;
    margin: 0;
    max-height: 95vh;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 16px;
  }

  .modal-footer {
    flex-direction: column;
  }

  .modal-cancel-btn,
  .modal-save-btn {
    width: 100%;
  }
}
