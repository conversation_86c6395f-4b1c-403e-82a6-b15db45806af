.reports-page {
  padding: 100px 32px 32px 32px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.reports-container {
  max-width: 1400px;
  margin: 0 auto;
}

.reports-container h1 {
  font-size: 24px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 24px;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.report-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.export-btn {
  padding: 8px 16px;
  background: none;
  border: none;
  color: #333;
  font-size: 14px;
  cursor: pointer;
  transition: color 0.2s;
}

.export-btn:hover {
  color: #007bff;
}

/* Table Container */
.table-container {
  background-color: white;
  border-radius: 40px;
  border: 1px solid #d3d3d3;
  box-shadow: 0 0 20px rgba(211, 211, 211, 0.8);
  margin-top: 1rem;
  position: relative;
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #E5E7EB;
  background-color: white;
  left: 0;
  right: 0;
  z-index: 10;
}

.table-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: var(--secondary-text-color);
  margin: 0;
}

.header-left .assets-count {
  font-size: 14px;
  color: #666;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* Search Box */
.search-box input {
  padding: 8px 16px;
  border: 1px solid #E5E7EB;
  border-radius: 20px;
  font-size: 14px;
  color: #374151;
  width: 240px;
  background-color: white;
}

.search-box input:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

.search-box input::placeholder {
  color: #9CA3AF;
}

/* Table Styles */
.table {
  width: 100%;
  min-width: 1200px;
  border-collapse: collapse;
}

.table th {
  background-color: rgba(211, 211, 211, 0.2);
  padding: 0.75rem 1rem;
  text-align: left;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--secondary-text-color);
  border-bottom: 1px solid #d3d3d3;
  text-transform: uppercase;
  white-space: nowrap;
}

.table td {
  padding: 0.75rem 1rem;
  font-size: 0.88rem;
  color: var(--secondary-text-color);
  border-bottom: 1px solid #d3d3d3;
  background-color: white;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.table tbody tr:hover {
  background-color: rgba(211, 211, 211, 0.1);
}

/* Asset Cell */
.asset-cell {
  display: flex;
  align-items: center;
}

.asset-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.asset-link:hover {
  text-decoration: underline;
}

/* User Cell */
.user-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-cell .user-icon {
  color: #6B7280;
  font-size: 14px;
}

/* Table Export Button */
.table-export-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.table-export-btn:hover {
  background-color: #f8f9fa;
  color: #007bff;
}

.table-export-btn svg {
  width: 16px;
  height: 16px;
}

/* Responsive */
@media (max-width: 1200px) {
  .table-container {
    overflow-x: auto;
  }

  .table {
    min-width: 1000px;
  }
}

/* User Info */
.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.user-icon {
  color: #0D6EFD;
  font-size: 16px;
}

.user-link {
  color: #0D6EFD;
  text-decoration: none;
}

.user-link:hover {
  text-decoration: underline;
}

.table-scroll-container {
  overflow-x: auto;
  white-space: nowrap;
}