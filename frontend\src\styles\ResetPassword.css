* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  border: none;
  color: var(--text-color);
  font-family: poppins, sans-serif;
}

.reset-page {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  background-color: var(--bg-color);
}

.reset-page section {
  display: flex;
  height: 100vh;
  width: 50vw;
  justify-content: center;
  align-items: center;
}

.reset-page .left-panel {
  background-color: white;
}

.reset-page .left-panel img {
  width: 100%;
  height: 50%;
  object-fit: cover;
}

.reset-page .right-panel {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.reset-page h2 {
  color: var(--text-color);
  margin-bottom: 20px;
}

.reset-page form {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 70%;
  gap: 20px;
}

.reset-page form fieldset {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: start;
  width: 100%;
  gap: 10px;
}

.reset-page form fieldset input {
  display: flex;
  width: 100%;
  height: 44px;
  padding: 18px 16px;
  border-radius: 40px;
  border: 1px solid #d3d3d3;
  background-color: var(--bg-color);
}

.reset-page input:focus {
  outline: 2px solid var(--primary-color);
}

.reset-page button {
  width: 100%;
  padding: 12px 32px;
  border-radius: 40px;
  background-color: var(--primary-color);
  color: var(--bg-color);
  font-size: 1rem;
  font-weight: 600;
  transition: ease 0.5s;
}

.reset-page div {
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 70%;
  gap: 10px;
  margin-top: 2rem;
}

.reset-page div a {
  color: var(--primary-color);
  font-size: 0.9rem;
}

.confirmation-box {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 70%;
    text-align: center;
  }
  
  .confirmation-box .resend-label {
    text-align: left;
    margin-top: 2rem;
  }

  .popup {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--bg-color);
    color: var(--text-color);
    border-radius: 8px;
    padding: 12px 20px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    z-index: 1000;
  }
  
  .popup.success {
    background-color: var(--bg-color);
    color: var(--success-color);
  }
  
  .popup.error {
    background-color: var(--bg-color);
    color: var(--error-color);
  }
  
  .popup button {
    background: none;
    border: none;
    margin-left: 10px;
    cursor: pointer;
    font-weight: bold;
    color: inherit;
  }
  