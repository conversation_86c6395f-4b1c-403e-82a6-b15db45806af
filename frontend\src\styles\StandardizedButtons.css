/* Standardized Check Out/Check In Buttons - Global Styling */
/* Based on Activate button styling from UserManagement.css */

/* Global Check-Out But<PERSON> - Green (matches Ticket module deployable-text color) */
.check-out-btn {
  padding: 8px 16px !important;
  background-color: var(--deployable-text) !important;
  color: white !important;
  border: none !important;
  border-radius: 40px !important;
  cursor: pointer !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  margin: 0 auto !important;
  display: inline-block !important;
  min-width: 80px !important;
  max-width: 90px !important;
  width: 90px !important;
  height: 32px !important;
  text-align: center !important;
  box-sizing: border-box !important;
  line-height: 1 !important;
}

.check-out-btn:hover {
  background-color: rgba(52, 199, 89, 0.8) !important;
}

/* Global Check-In Button - Primary Blue (matches Activate button exactly) */
.check-in-btn {
  padding: 8px 16px !important;
  background-color: var(--primary-color) !important;
  color: white !important;
  border: none !important;
  border-radius: 40px !important;
  cursor: pointer !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  margin: 0 auto !important;
  display: inline-block !important;
  min-width: 80px !important;
  max-width: 90px !important;
  width: 90px !important;
  height: 32px !important;
  text-align: center !important;
  box-sizing: border-box !important;
  line-height: 1 !important;
}

.check-in-btn:hover {
  background-color: var(--primary-color-hover) !important;
}

/* Disabled state for both buttons */
.check-out-btn:disabled,
.check-in-btn:disabled {
  background-color: #a9a9a9 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

.check-out-btn:disabled:hover,
.check-in-btn:disabled:hover {
  background-color: #a9a9a9 !important;
  transform: none !important;
}

/* Focus states for accessibility */
.check-out-btn:focus,
.check-in-btn:focus {
  outline: 2px solid rgba(0, 123, 255, 0.5);
  outline-offset: 2px;
}

/* Active states */
.check-out-btn:active,
.check-in-btn:active {
  transform: translateY(0) !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {

  .check-out-btn,
  .check-in-btn {
    padding: 6px 12px !important;
    font-size: 11px !important;
    min-width: 70px !important;
  }
}

/* Override any conflicting styles from other CSS files - Assets and Ticket modules */
.components-page .check-out-btn,
.components-page .check-in-btn,
.assets-page .check-out-btn,
.assets-page .check-in-btn,
.products-page .check-out-btn,
.products-page .check-in-btn,
.page .check-out-btn,
.page .check-in-btn,
.accessories-page .check-out-btn,
.accessories-page .check-in-btn,
.approved-tickets-page .check-out-btn,
.approved-tickets-page .check-in-btn {
  font-size: 11px !important;
  font-weight: 500 !important;
  padding: 6px 12px !important;
  border-radius: 20px !important;
  border: none !important;
  cursor: pointer !important;
  min-width: 70px !important;
  text-align: center !important;
  display: inline-flex !important;
  justify-content: center !important;
  align-items: center !important;
  margin: 0 auto !important;
  box-sizing: border-box !important;
  height: 28px !important;
  font-family: inherit !important;
  text-transform: none !important;
  white-space: nowrap !important;
  transition: all 0.3s ease !important;
  line-height: 1 !important;
  background-color: var(--deployable-text) !important;
  color: white !important;
}

/* Check-In buttons use primary blue color */
.components-page .check-in-btn,
.assets-page .check-in-btn,
.products-page .check-in-btn,
.page .check-in-btn,
.accessories-page .check-in-btn,
.approved-tickets-page .check-in-btn {
  background-color: var(--primary-color) !important;
}

/* Hover effects for Check-Out buttons (green) */
.components-page .check-out-btn:hover,
.assets-page .check-out-btn:hover,
.products-page .check-out-btn:hover,
.page .check-out-btn:hover,
.accessories-page .check-out-btn:hover,
.approved-tickets-page .check-out-btn:hover {
  background-color: rgba(52, 199, 89, 0.8) !important;
}

/* Hover effects for Check-In buttons (blue) */
.components-page .check-in-btn:hover,
.assets-page .check-in-btn:hover,
.products-page .check-in-btn:hover,
.page .check-in-btn:hover,
.accessories-page .check-in-btn:hover,
.approved-tickets-page .check-in-btn:hover {
  background-color: var(--primary-color-hover) !important;
}

/* Table button variants (for table-specific styling) */
.table-buttons-checkout,
.table-buttons-checkin {
  padding: 8px 16px !important;
  background-color: var(--deployable-text) !important;
  color: white !important;
  border: none !important;
  border-radius: 40px !important;
  cursor: pointer !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  margin: 0 auto !important;
  display: inline-flex !important;
  justify-content: center !important;
  align-items: center !important;
  min-width: 80px !important;
  text-align: center !important;
  box-sizing: border-box !important;
  height: auto !important;
}

.table-buttons-checkin {
  background-color: var(--primary-color) !important;
}

.table-buttons-checkout:hover {
  background-color: rgba(52, 199, 89, 0.8) !important;
}

.table-buttons-checkin:hover {
  background-color: var(--primary-color-hover) !important;
}

/* Checkout button in modals (slightly larger for modals but same style) */
.ticket-view-modal .checkout-btn,
.asset-view-modal .checkout-btn,
.ticket-view-modal .check-out-btn,
.asset-view-modal .check-out-btn,
.ticket-view-modal .check-in-btn,
.asset-view-modal .check-in-btn {
  padding: 10px 20px !important;
  background-color: var(--deployable-text) !important;
  color: white !important;
  border: none !important;
  border-radius: 40px !important;
  cursor: pointer !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  margin: 0 auto !important;
  display: block !important;
  min-width: 120px !important;
  max-width: 120px !important;
  width: 120px !important;
  height: 40px !important;
  text-align: center !important;
  box-sizing: border-box !important;
  line-height: 1 !important;
}

.ticket-view-modal .check-in-btn,
.asset-view-modal .check-in-btn {
  background-color: var(--primary-color) !important;
}

.ticket-view-modal .checkout-btn:hover,
.asset-view-modal .checkout-btn:hover,
.ticket-view-modal .check-out-btn:hover,
.asset-view-modal .check-out-btn:hover {
  background-color: rgba(52, 199, 89, 0.8) !important;
}

.ticket-view-modal .check-in-btn:hover,
.asset-view-modal .check-in-btn:hover {
  background-color: var(--primary-color-hover) !important;
}