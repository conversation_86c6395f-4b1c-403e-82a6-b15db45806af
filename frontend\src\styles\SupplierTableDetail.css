.modal-overlay {
  position: fixed;
  top: 60px;
  left: 0;
  width: 100%;
  height: calc(100vh - 60px);
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: flex-start;
  z-index: 1000;
  padding: 2rem 1rem;
  overflow-y: auto;
}

.modal-content {
  background-color: #fff;
  width: 100%;
  max-width: 1100px;
  padding: 2rem 2.5rem;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  font-family: 'Segoe UI', Tahoma, sans-serif;
  box-sizing: border-box;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.modal-header h2 {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #888;
  cursor: pointer;
  transition: color 0.2s;
}

.close-button:hover {
  color: #000;
}

.details-section {
  display: grid;
  grid-template-columns: max-content 1fr;
  row-gap: 1rem;
  column-gap: 1.5rem;
  font-size: 0.95rem;
  color: #333;
}

.details-section .label {
  font-weight: bold;
  text-transform: uppercase;
  color: #555;
  white-space: nowrap;
}

.details-section .value {
  word-break: break-word;
  color: #333;
}

.details-section a {
  color: #007bff;
  text-decoration: none;
}

.details-section a:hover {
  text-decoration: underline;
}
