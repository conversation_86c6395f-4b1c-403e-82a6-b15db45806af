.table-buttons-checkin img,
.table-buttons-checkout img,
.table-buttons-edit img,
.table-buttons-delete img,
.table-buttons-view img,
.table-buttons-audit img,
.table-buttons-delete-disabled img {
  display: flex;
  justify-self: center;
  height: 16px;
  width: 16px;
  object-fit: contain;
}

.table-buttons-audit img {
  width: 24px;
  height: 24px;
}

.table-buttons-edit,
.table-buttons-delete,
.table-buttons-view,
.table-buttons-delete-disabled,
.table-buttons-deactivate {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  width: 34px;
  height: 34px;
  margin: 0 auto;
  box-sizing: border-box;

  background-color: var(--bg-color);
  border: 1px solid #d3d3d3;

  transition: 0.5s ease;
}

.table-buttons-audit {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 34px;
  height: 34px;
  padding: 4px;
  border-radius: 4px;
  box-sizing: border-box;
}


.table-buttons-deactivate {
  width: auto;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  color: #dc3545;
  border-color: #dc3545;
  background-color: white;
}

.table-buttons-deactivate:hover {
  background-color: #dc3545;
  color: white;
}

.table-buttons-delete-disabled {
  cursor: default;
  position: relative;
}

.table-buttons-delete-disabled img {
  opacity: 0.5;
}

.table-buttons-delete-disabled::after {
  content: "";
  width: 100%;
  height: 100%;

  background-color: #d3d3d356;
  border-radius: 4px;
  border: 1px solid #d3d3d356;

  position: absolute;
  top: -1px;
  left: -1px;
}

/* Table Check-in/Check-out Button Styling - Now handled by StandardizedButtons.css */

.table-buttons-checkin-disabled {
  background-color: #a9a9a9 !important;
  color: white !important;
  border: none !important;
  padding: 8px 16px !important;
  border-radius: 40px !important;
  cursor: not-allowed !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  min-width: 80px !important;
  text-align: center !important;
  display: inline-flex !important;
  justify-content: center !important;
  align-items: center !important;
  margin: 0 auto !important;
  box-sizing: border-box !important;
  height: 32px !important;
}

.table-buttons-edit:hover,
.table-buttons-view:hover,
.table-buttons-audit:hover {
  background-color: rgba(0, 123, 255, 0.2);
}

.table-buttons-delete:hover {
  background-color: rgba(255, 0, 0, 0.2);
}

/* Specific styles for Products table buttons */
.products-table .table-buttons-edit,
.products-table .table-buttons-delete,
.products-table .table-buttons-view {
  padding: 4px;
  width: 26px;
  height: 26px;
  margin: 0 auto; /* Center the button in its cell */
}

/* Specific styles for Assets table buttons */
.assets-table .table-buttons-edit,
.assets-table .table-buttons-delete,
.assets-table .table-buttons-view {
  padding: 4px;
  width: 26px;
  height: 26px;
  margin: 0 auto; /* Center the button in its cell */
}

/* Specific styles for Accessories table buttons */
.accessories-page .table-buttons-edit,
.accessories-page .table-buttons-delete,
.accessories-page .table-buttons-view {
  padding: 4px !important;
  width: 26px !important;
  height: 26px !important;
  margin: 0 auto !important; /* Center the button in its cell */
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

/* Fix for button images in Accessories table */
.accessories-page .table-buttons-edit img,
.accessories-page .table-buttons-delete img,
.accessories-page .table-buttons-view img {
  width: 16px !important;
  height: 16px !important;
  object-fit: contain !important;
}
