/* Ticket View Page Specific Styles */

.ticket-view-content {
  display: flex;
  flex-direction: column;
  gap: 0;
}

/* Ticket View Header */
.ticket-view-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 30px;
  border-bottom: 1px solid #cccccc;
  background-color: white;
  border-radius: 8px 8px 0 0;
}

.ticket-view-tabs {
  display: flex;
  gap: 8px;
}

.ticket-tab-text {
  font-size: 1.25rem;
  font-weight: 400;
  color: var(--text-color);
}

.ticket-view-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.ticket-search {
  display: flex;
  align-items: center;
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 10px 12px;
  border: 1px solid #cccccc;
  font-size: 0.875rem;
  outline: none;
  width: 200px;
}

.ticket-search:focus {
  border-color: var(--primary-color);
}

/* Ticket View Table */
.ticket-view-table-wrapper {
  overflow-x: auto;
  max-height: 500px;
  overflow-y: auto;
  margin-top: 0;
}

.ticket-view-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.8125rem;
}

.ticket-view-table thead {
  background-color: #f0f1f2;
  position: sticky;
  top: 0;
  z-index: 1;
}

.ticket-view-table th {
  padding: 0.75rem !important;
  text-align: left !important;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-color);
  border-top: 1px solid #cccccc;
  border-bottom: 1px solid #cccccc;
  height: 50px;
  white-space: nowrap;
}

.ticket-view-table td {
  padding: 0.75rem;
  text-align: left !important;
  font-size: 0.8125rem;
  color: var(--text-color);
  border-bottom: 1px solid #cccccc;
  height: 50px;
  vertical-align: middle;
}

.ticket-view-table tbody tr:hover {
  background-color: #fcfcfc;
}



/* Description Section */
.ticket-description-section {
  padding: 20px 30px;
  border-bottom: 1px solid #cccccc;
  background-color: white;
}

.ticket-description-section h3 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 12px 0;
}

.ticket-description-section p {
  font-size: 0.875rem;
  color: var(--secondary-text-color);
  line-height: 1.5;
  margin: 0;
}

/* Pagination */
.ticket-view-pagination {
  padding: 15px 30px;
  border-top: 1px solid #cccccc;
  background-color: white;
  text-align: left;
  font-size: 0.875rem;
  color: #666;
  border-radius: 0 0 8px 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ticket-view-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .ticket-view-actions {
    width: 100%;
    justify-content: space-between;
  }

  .ticket-search {
    flex: 1;
  }
}
