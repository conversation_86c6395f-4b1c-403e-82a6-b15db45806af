.eof-warranty-report-table-section {
  overflow-x: auto;
  max-height: 55vh;
  overflow-y: auto;

  /* Prevent affecting the parent when scrolling. */
  overscroll-behavior: contain;


  table {
    width: 100%;
    border-collapse: collapse;

    th {
      padding: 0.75rem !important;
      /* 12px */
      text-align: left !important;
      font-size: 0.75rem;
      /* 12px */
      font-weight: 600;
      color: var(--text-color);
      border-top: 1px solid #cccccc;
      border-bottom: 1px solid #cccccc;
      background-color: #f0f1f2;

      height: 50px;

      position: sticky;
      top: 0;
      z-index: 1;
    }

    /* table header for 'Asset' */
    th:first-child {
      min-width: 200px;
    }

    /* table headear for 'Status'*/
    th:nth-child(2) {
      min-width: 200px;
      max-width: fit-content;
    }

    td {
      text-align: left !important;
      padding: 0.75rem;
      font-size: 0.8125rem;
      /* 13px */
      color: var(--text-color);
      border-bottom: 1px solid #cccccc;

      height: 50px;
    }

    /* Ensure empty-state row spans correctly across all columns */
    td.no-data-message {
      display: table-cell !important;
      width: 100% !important;
      white-space: normal !important;
      padding: 0.75rem !important;
      text-align: center !important;
      font-style: italic;
      color: var(--secondary-text-color);
      background: transparent;
    }

    tr:hover {
      background-color: #fcfcfc;
    }
  }
}