.view-modal {
  display: flex;
  height: 100vh;
  width: 100vw;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

.view-modal .overlay {
  display: flex;
  height: 100vh;
  width: 100vw;
  background-color: rgba(0, 0, 0, 0.3);
  position: absolute;
}

.view-modal .content {
  display: flex;
  flex-direction: column;
  max-height: 80%;
  width: fit-content;
  background-color: white;
  padding: 20px;
  border-radius: 40px;
  border: 1px solid #d3d3d3;
  position: absolute;
  z-index: 1001;
  overflow-y: auto;
}

.view-modal .close-button {
  height: 28px;
  width: 28px;
  padding: 5px;
  background-color: #ff0000;
  border-radius: 50px;
  position: absolute;
  right: 25px;
  top: 15px;
  transition: 0.5s ease;
  cursor: pointer;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
}

.view-modal .close-button img {
  width: 16px;
  height: 16px;
  filter: brightness(0) invert(1);
}

.view-modal .close-button:hover {
  background-color: darkred;
}

.view-modal .header-fieldset {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  border: none;
  padding: 0;
}

.view-modal .header-fieldset img {
  width: 100px;
  height: 100px;
  object-fit: cover;
  margin-right: 20px;
  border-radius: 25px;
}

.view-modal .header-fieldset h2 {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

/* When no image, center the title */
.view-modal .header-fieldset:not(:has(img)) {
  justify-content: center;
}

.view-modal .header-fieldset:not(:has(img)) h2 {
  text-align: center;
}

.view-modal .details-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 50vw;
  flex-wrap: wrap;
  overflow-y: auto;
  overflow-x: none;
}

.view-modal .left-content,
.view-modal .right-content {
  width: 48%;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.view-modal .detail-item {
  display: flex;
  width: 100%;
  align-items: center;
  flex-wrap: wrap;
  overflow-wrap: anywhere;
  padding: 20px;
  border: 1px solid #d3d3d3;
  border-radius: 25px;
  background-color: #f9f9f9;
}

.view-modal .detail-item label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 45%;
  font-weight: 600;
  color: var(--secondary-text-color);
}

.view-modal .detail-item label::after {
  content: ":";
}

.view-modal .detail-item p {
  margin-left: 10px;
  color: var(--secondary-text-color);
  width: 50%;
}

.view-modal .action-buttons {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}
