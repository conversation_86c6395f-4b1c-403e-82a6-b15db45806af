.view-audits-page {
  display: flex;
  flex-direction: column;
  height: auto;
  width: auto;
  padding: 100px 38px 38px 38px;
  background-color: var(--bg-color);
  overflow-y: auto;
}

.view-audits-page section {
  display: flex;
  flex-direction: row;
  height: auto;
  width: 100%;
  gap: 20px;
}

.view-audits-page .view-audits-content {
  display: flex;
  flex-direction: column;
  height: auto;
  width: 60vw;
  align-self: center;
  padding: 24px;
  margin-top: 20px;
  background-color: #ffffff;
  box-shadow: 0 0 20px rgba(211, 211, 211, 0.3);
  border-radius: 40px;
  border: 1px solid #d3d3d3;
}

.view-audits-page fieldset {
  display: flex;
  flex-direction: column;
  width: 100%;
  row-gap: 5px;
}

.view-audits-page fieldset label {
  color: var(--secondary-text-color);
  font-weight: 600;
}

.view-audits-page .view-audits-content fieldset p {
  padding: 13px 16px;
  border-radius: 10px;
  border: 1px solid #d3d3d3;
  font-size: 0.875rem;
  color: var(--secondary-text-color);
}

.view-audits-page .view-audits-content .attachments-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.view-audits-page .view-audits-content .attachments-container a {
  color: var(--primary-color);
  font-size: 0.875rem;
  text-decoration: underline !important;

  padding: 8px;
  border-radius: 10px;
  border: 1px solid #d3d3d3;

  width: 100%;
}

.view-audits-page .view-audits-content .attachments-container a::before {
  content: "📌 ";
}
