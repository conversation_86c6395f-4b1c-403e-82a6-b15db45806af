.table-layout {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #cccccc;
  width: 100%;

  .table-header {
    display: flex;
    align-items: center;
    padding: 15px 30px;
    border-radius: 8px 8px 0 0;

    input[type="search"] {
      display: flex;
      align-items: center;
      background-color: #f9f9f9;
      border-radius: 4px;
      padding: 10px 12px;
      border: none;
      border: 1px solid #cccccc;
    }
  }

  .supplier-page-table-section {
    overflow-x: auto;
    max-height: 55vh;
    overflow-y: auto;

    /* Prevent affecting the parent when scrolling. */
    overscroll-behavior: contain;

    table {
      width: 100%;
      border-collapse: collapse;

      th {
        padding: 0.75rem !important;
        text-align: left !important;
        font-size: 0.75rem;
        font-weight: 600;
        color: var(--text-color);
        border-top: 1px solid #cccccc;
        border-bottom: 1px solid #cccccc;
        background-color: #f0f1f2;

        height: 50px;

        position: sticky;
        top: 0;
        z-index: 1;
      }

      .supplier-name {
        display: flex;
        align-items: center;
        gap: 5px;

        .supplier-name-link {
          color: var(--primary-color);
          cursor: pointer;
        }

        .supplier-name-link:hover {
          color: var(--primary-color-hover);
        }
      }

      .supplier-name span {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      /* Checkbox Column */
      th:nth-child(1) {
        width: 10px;
      }

      /* Name and Contact Person Column */
      th:nth-child(2),
      td:nth-child(2),
      th:nth-child(8),
      td:nth-child(8) {
        min-width: 150px;
        max-width: 180px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      /* URL Column */
      th:nth-child(11),
      td:nth-child(11) {
        min-width: 150px;
        max-width: 180px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      /* Actions Button Column */
      th:last-child {
        width: 50px;
      }

      td {
        text-align: left !important;
        padding: 0.75rem;
        font-size: 0.8125rem;
        color: var(--text-color);
        border-bottom: 1px solid #cccccc;

        height: 50px;

        span {
          cursor: pointer;
        }

        span:hover {
          color: var(--primary-color);
        }
      }

      /* Ensure empty-state row spans correctly across all columns */
      td.no-data-message {
        display: table-cell !important;
        width: 100% !important;
        white-space: normal !important;
        padding: 0.75rem !important;
        text-align: center !important;
        font-style: italic;
        color: var(--secondary-text-color);
        background: transparent;
      }

      tr:hover {
        background-color: #fcfcfc;
      }

      img {
        width: 22px;
        height: 22px;
      }

      .action-button-section,
      .category-name {
        display: flex;
        flex-direction: row;
        align-items: center;
        color: var(--text-color);
        gap: 10px;
      }

      .action-button {
        border: none;
        border: 1px solid #cccccc;
        background-color: #f9f9f9;
        padding: 5px 10px;
        border-radius: 4px;
        color: #555555;
        font-size: 0.75rem;
        cursor: pointer;
        transition: background-color 0.2s ease, color 0.2s ease,
          border-color 0.2s ease;
      }

      .action-button:disabled {
        cursor: not-allowed;
        position: relative;
      }

      .action-button:disabled::after {
        content: "";
        width: 100%;
        height: 100%;

        background-color: #d3d3d367;
        border-radius: 4px;
        border: 1px solid #d3d3d356;

        position: absolute;
        top: -1px;
        left: -1px;
      }

      .action-button:hover:not(.action-button:hover:disabled) {
        background-color: #f0f0f0;
        color: #333333;
        border-color: #999999;
      }
    }
  }

  .table-pagination {
    padding: 0 20px;
  }
}