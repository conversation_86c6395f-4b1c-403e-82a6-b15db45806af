.activity-table-container {
  background: white;
  border-radius: 40px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 0;
  overflow: hidden;
}

.activity-table-container h2 {
  color: #111827;
  font-size: 1.125rem;
  font-weight: 500;
  margin: 0;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #E5E7EB;
}

.activity-table-wrapper {
  margin: 0;
  padding: 0;
  overflow-x: auto;
}

.activity-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
  margin: 0;
  border-radius: 0;
  background: white;
}

.activity-table thead,
.activity-table tbody,
.activity-table tr,
.activity-table th,
.activity-table td {
  border-radius: 0;
}

.activity-table th {
  background-color: #F9FAFB;
  padding: 0.75rem 2rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: #6B7280;
  text-align: left;
  border-bottom: 1px solid #E5E7EB;
  white-space: nowrap;
}

.activity-table td {
  padding: 0.75rem 2rem;
  font-size: 0.875rem;
  color: #374151;
  border-bottom: 1px solid #E5E7EB;
  background: white;
}

.activity-table tr:hover td {
  background-color: #F9FAFB;
}

.activity-table tr:last-child td {
  border-bottom: none;
}

/* Fix for Notes column spacing */
.activity-table th:last-child,
.activity-table td:last-child {
  padding-right: 30px !important;
  text-align: center;
  min-width: 80px;
}

/* Status badge styles */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  padding: 0.375rem 0.75rem;
  border-radius: 0;
  white-space: nowrap;
}

.status-badge.checked {
  background-color: rgba(34, 197, 94, 0.1);
  color: #16a34a;
}

.status-badge.unchecked {
  background-color: rgba(59, 130, 246, 0.1);
  color: #2563eb;
}

/* Browse all button container */
.browse-all-container {
  padding: 1.5rem 2rem;
  background: white;
  border-top: 1px solid #E5E7EB;
}

.browse-all-button {
  display: block;
  width: 100%;
  padding: 0.75rem;
  background: white;
  border: 1px solid #E5E7EB;
  border-radius: 40px;
  color: #6B7280;
  font-weight: 500;
  font-size: 0.875rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
}

.browse-all-button:hover {
  background-color: #F9FAFB;
  border-color: #D1D5DB;
  color: #374151;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}