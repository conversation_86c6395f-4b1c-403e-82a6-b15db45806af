.asset-info-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.asset-info-card h3 {
  color: #666;
  font-size: 1rem;
  margin: 0 0 1rem 0;
}

.asset-info-value {
  color: #1a73e8;
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.asset-info-progress {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.progress {
  height: 100%;
  background-color: #1a73e8;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.percentage {
  color: #666;
  font-size: 0.9rem;
  font-weight: 500;
  min-width: 45px;
} 