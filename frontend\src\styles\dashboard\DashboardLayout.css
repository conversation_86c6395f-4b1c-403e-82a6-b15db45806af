/* Dashboard Grid Layout */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(4, 248px) 295px;
  grid-template-rows: repeat(2, 100px);
  gap: 72px;
  padding: 2rem 3rem;
  margin: 0 3rem;
  justify-content: start;
}

/* Responsive adjustments */
@media (max-width: 1400px) {
  .dashboard-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    padding: 1.5rem 2rem;
    margin: 0 2rem;
  }
}

@media (max-width: 992px) {
  .dashboard-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
    padding: 1.25rem 1.5rem;
    margin: 0 1.5rem;
  }
}

@media (max-width: 576px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1rem;
    margin: 0 1rem;
  }
} 