.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(4, 250px) 280px;
  gap: 32px;
  padding: 1rem;
}

.status-card {
  background: white;
  border-radius: 30px;
  padding: 1.75rem 1.25rem;
  display: flex;
  align-items: center;
  gap: 1.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  border: 1px solid #e8e8e8;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
  height: 100px;
  animation: fadeInUp 0.6s ease both;
  width: 100%;
  overflow: hidden;
}

.status-card .content-wrapper {
  display: flex;
  align-items: center;
  gap: 1.75rem;
  transition: transform 0.3s ease;
  width: 100%;
}

/* First row */
.status-card:nth-of-type(1) { grid-column: 1; grid-row: 1; } /* Due for Return */
.status-card:nth-of-type(2) { grid-column: 2; grid-row: 1; } /* Upcoming Audits */
.status-card:nth-of-type(3) { grid-column: 3; grid-row: 1; } /* Upcoming End of Life */
.status-card:nth-of-type(4) { grid-column: 4; grid-row: 1; } /* Expiring Warranties */

/* Second row */
.status-card:nth-of-type(5) { grid-column: 1; grid-row: 2; } /* Overdue for Return */
.status-card:nth-of-type(6) { grid-column: 2; grid-row: 2; } /* Overdue Audits */
.status-card:nth-of-type(7) { grid-column: 3; grid-row: 2; } /* Reached End of Life */
.status-card:nth-of-type(8) { grid-column: 4; grid-row: 2; } /* Expired Warranties */

/* Low Stock card */
.status-card:nth-of-type(9) { 
  grid-column: 5;
  grid-row: 1 / span 2;
  height: 100%;
  width: 100%;
} /* Low Stock */

/* Status Number */
.status-number {
  padding: 0.5rem;
  border-radius: 8px;
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  min-width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.status-number.blue {
  background: #0066ff;
  animation: pulseBlue 2s ease-in-out infinite;
}

.status-number.red {
  background: #ff3333;
  animation: pulseRed 2s ease-in-out infinite;
}

.status-title {
  font-size: 0.85rem;
  color: #333;
  font-weight: 500;
  line-height: 1.2;
  flex: 1;
}

/* Animations */
@keyframes pulseBlue {
  0% { box-shadow: 0 0 0 0 rgba(0, 102, 255, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(0, 102, 255, 0); }
  100% { box-shadow: 0 0 0 0 rgba(0, 102, 255, 0); }
}

@keyframes pulseRed {
  0% { box-shadow: 0 0 0 0 rgba(255, 51, 51, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(255, 51, 51, 0); }
  100% { box-shadow: 0 0 0 0 rgba(255, 51, 51, 0); }
}

@keyframes pulseWhite {
  0% { box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(255, 255, 255, 0); }
  100% { box-shadow: 0 0 0 0 rgba(255, 255, 255, 0); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulseWhiteBox {
  0% { 
    transform: translateY(-50%) scaleY(0.9);
  }
  50% { 
    transform: translateY(-50%) scaleY(1.1);
  }
  100% { 
    transform: translateY(-50%) scaleY(0.9);
  }
}

@keyframes pulseWhiteNumber {
  0% { box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6); }
  70% { box-shadow: 0 0 0 10px rgba(255, 255, 255, 0); }
  100% { box-shadow: 0 0 0 0 rgba(255, 255, 255, 0); }
}

/* Hover Effects */
.status-card:hover {
  background: #0066ff;
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 102, 255, 0.25);
}

.status-card:hover::before {
  content: '';
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 40%;
  background: white;
  border-radius: 0 2px 2px 0;
  animation: pulseWhiteBox 2s ease-in-out infinite;
}

.status-card:hover .status-number,
.status-card.active .status-number {
  background: white;
  color: #0066ff;
  animation: pulseWhite 2s ease-in-out infinite;
}

.status-card:hover .status-title {
  color: white;
}

.status-card:hover .content-wrapper {
  transform: translateX(8px);
}

/* Active State (When Popup is Open) */
.status-card.active {
  background: #0066ff;
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 102, 255, 0.25);
}

.status-card.active::before {
  content: '';
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 40%;
  background: white;
  border-radius: 0 2px 2px 0;
  animation: pulseWhiteBox 2s ease-in-out infinite;
}

.status-card.active .status-title {
  color: white;
}

.status-card.active .content-wrapper {
  transform: translateX(8px);
}

/* Responsive adjustments */
@media (max-width: 1400px) {
  .status-card,
  .status-card:nth-child(9) {
    width: 100%;
  }
  
  .status-card:nth-child(9) {
    grid-area: 3 / 1 / 3 / 5;
    height: 100px;
  }
}

@media (max-width: 992px) {
  .status-card:nth-child(9) {
    grid-area: 5 / 1 / 5 / 3;
  }
}

@media (max-width: 576px) {
  .status-card:nth-child(9) {
    grid-area: auto;
  }
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.user-icon {
  width: 1rem;
  height: 1rem;
  color: #0D6EFD !important;
}

.user-info span {
  color: #0D6EFD;
  font-weight: 500;
  font-size: 0.875rem;
}

.location-info {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  margin-top: 0.25rem;
}

.location-info svg {
  width: 1rem;
  height: 1rem;
  color: #0D6EFD !important;
}

.location-info span {
  color: #0D6EFD;
  font-weight: 500;
  font-size: 0.875rem;
} 