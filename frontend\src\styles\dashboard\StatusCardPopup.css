.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.popup-content {
  background: white;
  border-radius: 24px;
  width: 90%;
  max-width: 600px;
  border: 1px solid #d3d3d3;
  box-shadow: 0 0 20px rgba(211, 211, 211, 0.8);
  animation: slideIn 0.3s ease;
  padding: 0;
  overflow: hidden;
  position: relative;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid #d3d3d3;
  background: white;
}

.popup-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: var(--secondary-text-color);
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  color: #6B7280;
  cursor: pointer;
  padding: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s;
  border-radius: 50%;
}

.close-button:hover {
  color: #111827;
  background-color: #f3f4f6;
}

.close-button svg {
  width: 1.25rem;
  height: 1.25rem;
}

.popup-body {
  margin: 0;
  padding: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.audits-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
  margin: 0;
  border-radius: 0;
  background: white;
  border-spacing: 0;
}

/* Global rule to ensure all elements in the table are left-aligned */
.audits-table *,
.audits-table * * {
  text-align: left !important;
}

.audits-table thead,
.audits-table tbody,
.audits-table tr,
.audits-table th,
.audits-table td {
  border-radius: 0;
}

.audits-table th {
  background-color: rgba(211, 211, 211, 0.2);
  padding: 0.75rem 1rem;
  text-align: left;
  font-size: 0.75rem;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #d3d3d3;
  text-transform: uppercase;
  white-space: nowrap;
}

/* Ensure all table headers are left-aligned */
.audits-table th {
  text-align: left !important;
}

.audits-table td {
  padding: 0.75rem 1rem;
  font-size: 0.88rem;
  color: var(--secondary-text-color);
  border-bottom: 1px solid #d3d3d3;
  background: white;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left;
}

/* Ensure all table cells are left-aligned */
.audits-table td {
  text-align: left !important;
}

/* Remove bottom border from last row */
.audits-table tr:last-child td {
  border-bottom: none;
}

/* Add hover effect */
.audits-table tr:hover td {
  background-color: rgba(211, 211, 211, 0.1);
}

/* Fix the first and last cells to match the popup border radius */
.audits-table tr:first-child th:first-child {
  border-top-left-radius: 0;
}

.audits-table tr:first-child th:last-child {
  border-top-right-radius: 0;
}

.audits-table tr:last-child td:first-child {
  border-bottom-left-radius: 0;
}

.audits-table tr:last-child td:last-child {
  border-bottom-right-radius: 0;
}

.date-cell {
  color: #374151;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
}

.date-header {
  text-align: left;
}

/* Category info styles */
.category-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.category-icon {
  color: #6B7280;
  font-size: 1.125rem;
}

.category-name {
  color: #111827;
  font-weight: 500;
}

/* Asset info styles */
.asset-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #0D6EFD;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.asset-info:hover {
  text-decoration: underline;
}

.asset-info span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.asset-icon {
  color: #0D6EFD !important;
  font-size: 1.125rem;
  flex-shrink: 0;
}

/* Location info styles */
.location-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #0D6EFD;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.location-info:hover {
  text-decoration: underline;
}

.location-info svg {
  width: 1.125rem !important;
  height: 1.125rem !important;
  color: #0D6EFD !important;
  flex-shrink: 0;
}

.location-name {
  color: #0D6EFD;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* User info styles */
.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-color);
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-info:hover {
  text-decoration: underline;
}

.user-icon {
  width: 1.125rem !important;
  height: 1.125rem !important;
  color: var(--primary-color) !important;
  flex-shrink: 0;
}

.user-name {
  color: var(--primary-color);
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Column widths */
.audits-table th:nth-child(1),
.audits-table td:nth-child(1) {
  width: 40%;
  text-align: left;
}

.audits-table th:nth-child(2),
.audits-table td:nth-child(2) {
  width: 30%;
  text-align: left;
}

.audits-table th:nth-child(3),
.audits-table td:nth-child(3) {
  width: 30%;
  text-align: left;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive styles */
@media (max-width: 576px) {
  .popup-content {
    width: 95%;
    max-width: 95%;
  }

  .audits-table th,
  .audits-table td {
    padding: 0.75rem 0.5rem;
  }

  .audits-table th:nth-child(1),
  .audits-table td:nth-child(1) {
    width: 35%;
    text-align: left;
  }

  .audits-table th:nth-child(2),
  .audits-table td:nth-child(2) {
    width: 30%;
    text-align: left;
  }

  .audits-table th:nth-child(3),
  .audits-table td:nth-child(3) {
    width: 35%;
    text-align: left;
  }
}