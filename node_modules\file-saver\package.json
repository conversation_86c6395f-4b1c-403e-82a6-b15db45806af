{"name": "file-saver", "version": "2.0.5", "description": "An HTML5 saveAs() FileSaver implementation", "main": "dist/FileSaver.min.js", "files": ["dist/FileSaver.js", "dist/FileSaver.min.js", "dist/FileSaver.min.js.map", "src/FileSaver.js"], "scripts": {"test": "echo \"Error: no test specified\" && exit 0", "build:development": "babel -o dist/FileSaver.js --plugins @babel/plugin-transform-modules-umd src/FileSaver.js", "build:production": "babel -o dist/FileSaver.min.js -s --plugins @babel/plugin-transform-modules-umd --presets minify src/FileSaver.js", "build": "npm run build:development && npm run build:production", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "https://github.com/eligrey/FileSaver.js"}, "keywords": ["filesaver", "saveas", "blob"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/eligrey/FileSaver.js/issues"}, "homepage": "https://github.com/eligrey/FileSaver.js#readme", "devDependencies": {"@babel/cli": "^7.1.0", "@babel/core": "^7.1.0", "@babel/plugin-transform-modules-umd": "^7.1.0", "babel-preset-minify": "^0.4.3"}}